TITLE: Starting n8n after installation (Bash)
DESCRIPTION: These commands initiate the n8n application after it has been installed globally. Both commands achieve the same result, launching the n8n server and making the UI accessible.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/npm.md#_snippet_4

LANGUAGE: bash
CODE:
```
n8n
# or
n8n start
```

----------------------------------------

TITLE: Referencing External Secrets in n8n Expressions (JavaScript)
DESCRIPTION: This snippet demonstrates how to reference an external secret within an n8n credential field using an expression. The expression allows dynamic retrieval of secret values from configured vaults. Replace <vault-name> with the specific vault provider (e.g., vault, infisical, awsSecretsManager) and <secret-name> with the actual name of the secret as stored in the vault.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/external-secrets.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
{{ $secrets.<vault-name>.<secret-name> }}
```

----------------------------------------

TITLE: Calling n8n API using API Key (Shell)
DESCRIPTION: This shell script demonstrates how to make a GET request to the n8n API to retrieve active workflows. It shows examples for both self-hosted and n8n Cloud instances, including how to pass the API key in the 'X-N8N-API-KEY' header.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/api/authentication.md#_snippet_0

LANGUAGE: shell
CODE:
```
# For a self-hosted n8n instance
curl -X 'GET' \
  '<N8N_HOST>:<N8N_PORT>/<N8N_PATH>/api/v<version-number>/workflows?active=true' \
  -H 'accept: application/json' \
  -H 'X-N8N-API-KEY: <your-api-key>'

# For n8n Cloud
curl -X 'GET' \
  '<your-cloud-instance>/api/v<version-number>/workflows?active=true' \
  -H 'accept: application/json' \
  -H 'X-N8N-API-KEY: <your-api-key>'
```

----------------------------------------

TITLE: GitHub Action to Pull N8n Version Control Changes (YAML)
DESCRIPTION: This GitHub Action workflow automates pulling version control changes into an n8n instance. It triggers on pushes to the 'production' branch or can be dispatched manually. It uses `curl` to send a POST request to the n8n instance's version control pull endpoint, authenticating with API keys stored as GitHub secrets.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_snippets/source-control-environments/github-action.md#_snippet_0

LANGUAGE: yaml
CODE:
```
name: CI
on:
  # Trigger the workflow on push or pull request events for the "production" branch
  push:
    branches: [ "production" ]
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:
jobs:
  run-pull:
    runs-on: ubuntu-latest
    steps:
      - name: PULL
		# Use GitHub secrets to protect sensitive information
        run: >
          curl --location '${{ secrets.INSTANCE_URL }}/version-control/pull' --header
          'Content-Type: application/json' --header 'X-N8N-API-KEY: ${{ secrets.INSTANCE_API_KEY }}'
```

----------------------------------------

TITLE: Example n8n Workflow JSON Structure (JSON)
DESCRIPTION: This JSON snippet illustrates the detailed structure of an n8n workflow, including its ID, name, active status, and an array of configured nodes. Each node specifies its parameters, name, type, type version, position, and sometimes credentials, demonstrating various n8n node types like Start, HTTP Request, Airtable, IF, Set, Function, Discord, and Cron, along with their connections.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/embed/managing-workflows.md#_snippet_2

LANGUAGE: JSON
CODE:
```
{
  "data": {
    "id": "1012",
    "name": "Nathan's Workflow",
    "active": false,
    "nodes": [
      {
        "parameters": {},
        "name": "Start",
        "type": "n8n-nodes-base.start",
        "typeVersion": 1,
        "position": [
          130,
          640
        ]
      },
      {
        "parameters": {
          "authentication": "headerAuth",
          "url": "https://internal.users.n8n.cloud/webhook/custom-erp",
          "options": {
            "splitIntoItems": true
          },
          "headerParametersUi": {
            "parameter": [
              {
                "name": "unique_id",
                "value": "recLhLYQbzNSFtHNq"
              }
            ]
          }
        },
        "name": "HTTP Request",
        "type": "n8n-nodes-base.httpRequest",
        "typeVersion": 1,
        "position": [
          430,
          300
        ],
        "credentials": {
          "httpHeaderAuth": "beginner_course"
        }
      },
      {
        "parameters": {
          "operation": "append",
          "application": "appKBGQfbm6NfW6bv",
          "table": "processingOrders",
          "options": {}
        },
        "name": "Airtable",
        "type": "n8n-nodes-base.airtable",
        "typeVersion": 1,
        "position": [
          990,
          210
        ],
        "credentials": {
          "airtableApi": "Airtable"
        }
      },
      {
        "parameters": {
          "conditions": {
            "string": [
              {
                "value1": "={{$json[\"orderStatus\"]}}",
                "value2": "processing"
              }
            ]
          }
        },
        "name": "IF",
        "type": "n8n-nodes-base.if",
        "typeVersion": 1,
        "position": [
          630,
          300
        ]
      },
      {
        "parameters": {
          "keepOnlySet": true,
          "values": {
            "number": [
              {
                "name": "=orderId",
                "value": "={{$json[\"orderID\"]}}"
              }
            ],
            "string": [
              {
                "name": "employeeName",
                "value": "={{$json[\"employeeName\"]}}"
              }
            ]
          },
          "options": {}
        },
        "name": "Set",
        "type": "n8n-nodes-base.set",
        "typeVersion": 1,
        "position": [
          800,
          210
        ]
      },
      {
        "parameters": {
          "functionCode": "let totalBooked = items.length;\nlet bookedSum = 0;\n\nfor(let i=0; i < items.length; i++) {\n  bookedSum = bookedSum + items[i].json.orderPrice;\n}\nreturn [{json:{totalBooked, bookedSum}}]\n"
        },
        "name": "Function",
        "type": "n8n-nodes-base.function",
        "typeVersion": 1,
        "position": [
          800,
          400
        ]
      },
      {
        "parameters": {
          "webhookUri": "https://discord.com/api/webhooks/865213348202151968/oD5_WPDQwtr22Vjd_82QP3-_4b_lGhAeM7RynQ8Js5DzyXrQEnj0zeAQIA6fki1JLtXE",
          "text": "=This week we have {{$json[\"totalBooked\"]}} booked orders with a total value of {{$json[\"bookedSum\"]}}. My Unique ID: {{$node[\"HTTP Request\"].parameter[\"headerParametersUi\"][\"parameter\"][0][\"value\"]}}"
        },
        "name": "Discord",
        "type": "n8n-nodes-base.discord",
        "typeVersion": 1,
        "position": [
          1000,
          400
        ]
      },
      {
        "parameters": {
          "triggerTimes": {
            "item": [
              {
                "mode": "everyWeek",
                "hour": 9
              }
            ]
          }
        },
        "name": "Cron",
        "type": "n8n-nodes-base.cron",
        "typeVersion": 1,
        "position": [
          220,
          300
        ]
      }
    ],
    "connections": {
      "HTTP Request": {
        "main": [
          [
            {
              "node": "IF",
              "type": "main",
              "index": 0
            }
          ]
        ]
      },
      "Start": {
        "main": [
          []
        ]
      },
      "IF": {

```

----------------------------------------

TITLE: Managing Custom Execution Data in JavaScript
DESCRIPTION: This method allows setting and getting custom data associated with the current workflow execution. Refer to Custom executions data for more information on its usage and persistence.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/n8n-metadata.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
$execution.customData
```

----------------------------------------

TITLE: Verifying Docker and Docker Compose Installation
DESCRIPTION: This snippet shows how to verify that Docker and Docker Compose have been successfully installed by checking their versions from the command line.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/docker-compose.md#_snippet_1

LANGUAGE: bash
CODE:
```
docker --version
docker compose version
```

----------------------------------------

TITLE: Configuring OpenAI Assistant Instructions
DESCRIPTION: This snippet provides an example of system instructions for an OpenAI Assistant. It defines the assistant's persona, communication style, and how it should handle user queries, emphasizing a friendly, concise, and supportive tone while avoiding jargon.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-langchain.openai/assistant-operations.md#_snippet_1

LANGUAGE: Plain Text
CODE:
```
Always respond in a friendly and engaging manner. When a user asks a question, provide a concise answer first, followed by a brief explanation or additional context if necessary. If the question is open-ended, offer a suggestion or ask a clarifying question to guide the conversation. Keep the tone positive and supportive, and avoid technical jargon unless specifically requested by the user.
```

----------------------------------------

TITLE: Defining an n8n Workflow with Multiple Nodes (JSON)
DESCRIPTION: This JSON snippet defines an n8n workflow, illustrating the configuration of various nodes such as conditional logic ('If'), time delays ('Wait'), data manipulation ('Edit Fields'), and scheduled triggers. It also details the connections between these nodes, outlining the flow of data and execution within the workflow. The snippet demonstrates how to set parameters for each node, including specific values for assignments, wait times, and scheduling rules.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-2.md#_snippet_5

LANGUAGE: json
CODE:
```
		"type": "n8n-nodes-base.if",
		"typeVersion": 2,
		"position": [
			1280,
			360
		]
		},
		{
		"parameters": {
			"amount": 1,
			"unit": "minutes"
		},
		"id": "5aa860b7-c73c-4df0-ad63-215850166f13",
		"name": "Wait",
		"type": "n8n-nodes-base.wait",
		"typeVersion": 1.1,
		"position": [
			1480,
			260
		],
		"webhookId": "be78732e-787d-463e-9210-2c7e8239761e"
		},
		{
		"parameters": {
			"assignments": {
			"assignments": [
				{
				"id": "e058832a-2461-4c6d-b584-043ecc036427",
				"name": "outputValue",
				"value": "={{ $json['new-date'] }}",
				"type": "string"
				}
			]
			},
			"includeOtherFields": true
```

----------------------------------------

TITLE: n8n Workflow Configuration JSON
DESCRIPTION: This JSON object represents a complete n8n workflow, providing the configuration details for various nodes including Gmail, Manual Trigger, HTTP Request, and Airtable. It can be imported into the n8n Editor UI to replicate the described workflow for generating total sales files and sending notifications.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-5/chapter-5.2.md#_snippet_0

LANGUAGE: JSON
CODE:
```
    {
    "meta": {
        "templateCredsSetupCompleted": true,
        "instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"
    },
    "nodes": [
        {
        "parameters": {
            "sendTo": "<EMAIL>",
            "subject": "Your TPS Reports",
            "emailType": "text",
            "message": "Please find your TPS report attached.",
            "options": {
            "attachmentsUi": {
                "attachmentsBinary": [
                {}
                ]
            }
            }
        },
        "id": "d889eb42-8b34-4718-b961-38c8e7839ea6",
        "name": "Gmail",
        "type": "n8n-nodes-base.gmail",
        "typeVersion": 2.1,
        "position": [
            2100,
            500
        ],
        "credentials": {
            "gmailOAuth2": {
            "id": "HFesCcFcn1NW81yu",
            "name": "Gmail account 7"
            }
        }
        },
        {
        "parameters": {},
        "id": "c0236456-40be-4f8f-a730-e56cb62b7b5c",
        "name": "When clicking \"Execute workflow\"",
        "type": "n8n-nodes-base.manualTrigger",
        "typeVersion": 1,
        "position": [
            780,
            600
        ]
        },
        {
        "parameters": {
            "url": "https://internal.users.n8n.cloud/webhook/level2-erp",
            "authentication": "genericCredentialType",
            "genericAuthType": "httpHeaderAuth",
            "sendHeaders": true,
            "headerParameters": {
            "parameters": [
                {
                "name": "unique_id",
                "value": "recFIcD6UlSyxaVMQ"
                }
            ]
            },
            "options": {}
        },
        "id": "cc106fa0-6630-4c84-aea4-a4c7a3c149e9",
        "name": "HTTP Request",
        "type": "n8n-nodes-base.httpRequest",
        "typeVersion": 4.1,
        "position": [
            1000,
            500
        ],
        "credentials": {
            "httpHeaderAuth": {
            "id": "qeHdJdqqqaTC69cm",
            "name": "Course L2 Credentials"
            }
        }
        },
        {
        "parameters": {
            "operation": "search",
            "base": {
            "__rl": true,
            "value": "apprtKkVasbQDbFa1",
            "mode": "list",
            "cachedResultName": "All your base",
            "cachedResultUrl": "https://airtable.com/apprtKkVasbQDbFa1"
            },
            "table": {
            "__rl": true,
            "value": "tblInZ7jeNdlUOvxZ",
            "mode": "list",
            "cachedResultName": "Course L2, Workflow 1",
            "cachedResultUrl": "https://airtable.com/apprtKkVasbQDbFa1/tblInZ7jeNdlUOvxZ"
            },
            "options": {}
        },
        "id": "e5ae1927-b531-401c-9cb2-ec1f2836ba6",
```

----------------------------------------

TITLE: n8n Workflow: Merging Customer Data and Custom Code (JSON)
DESCRIPTION: This n8n workflow demonstrates how to merge data from a 'Customer Datastore' node and a 'Code' node. It uses a 'Merge' node configured to combine inputs based on matching 'name' fields. The 'Code' node generates custom data in a specific format, which is then merged with customer records. This example shows how to set up a complete data merging process within n8n.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-3.md#_snippet_0

LANGUAGE: JSON
CODE:
```
{
  "meta": {
    "templateCredsSetupCompleted": true,
    "instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"
  },
  "nodes": [
    {
      "parameters": {
        "mode": "combine",
        "mergeByFields": {
          "values": [
            {
              "field1": "name",
              "field2": "name"
            }
          ]
        },
        "options": {}
      },
      "id": "578365f3-26dd-4fa6-9858-f0a5fdfc413b",
      "name": "Merge",
      "type": "n8n-nodes-base.merge",
      "typeVersion": 2.1,
      "position": [
        720,
        580
      ]
    },
    {
      "parameters": {},
      "id": "71aa5aad-afdf-4f8a-bca0-34450eee8acc",
      "name": "When clicking \"Execute workflow\"",
      "type": "n8n-nodes-base.manualTrigger",
      "typeVersion": 1,
      "position": [
        260,
        560
      ]
    },
    {
      "parameters": {
        "operation": "getAllPeople"
      },
      "id": "497174fe-3cab-4160-8103-78b44efd038d",
      "name": "Customer Datastore (n8n training)",
      "type": "n8n-nodes-base.n8nTrainingCustomerDatastore",
      "typeVersion": 1,
      "position": [
        500,
        460
      ]
    },
    {
      "parameters": {
        "jsCode": "return [\n  {\n    'name': 'Jay Gatsby',\n    'language': 'English',\n    'country': {\n      'code': 'US',\n      'name': 'United States'\n    }\n    \n  }\n  \n];"
      },
      "id": "387e8a1e-e796-4f05-8e75-7ce25c786c5f",
      "name": "Code",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        500,
        720
      ]
    }
  ],
  "connections": {
    "When clicking \"Execute workflow\"": {
      "main": [
        [
          {
            "node": "Customer Datastore (n8n training)",
            "type": "main",
            "index": 0
          },
          {
            "node": "Code",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Customer Datastore (n8n training)": {
      "main": [
        [
          {
            "node": "Merge",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Code": {
      "main": [
        [
          {
            "node": "Merge",
            "type": "main",
            "index": 1
          }
        ]
      ]
    }
  },
  "pinData": {}
}
```

----------------------------------------

TITLE: Retrieve First Item from Node - JavaScript
DESCRIPTION: These methods retrieve data items from a specified upstream node. They allow for optional branchIndex and runIndex parameters to target specific outputs or workflow runs. When branchIndex is omitted, the method defaults to the output directly connecting the source node to the current node, making them suitable for use within the n8n Code node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/output-other-nodes.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
$("<node-name>").first(branchIndex?, runIndex?)
```

----------------------------------------

TITLE: Accessing Current Timestamp in n8n Code Node (JavaScript)
DESCRIPTION: This example shows how to use the `$now` Luxon object within a JavaScript Code node. Directly referencing `$now` outputs an ISO formatted timestamp. When `$now` is implicitly converted to a string (e.g., through concatenation), it yields a Unix timestamp, similar to its behavior in expressions.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/luxon.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
$now
// n8n displays <ISO formatted timestamp>
// For example 2022-03-09T14:00:25.058+00:00
let rightNow = "Today's date is " + $now
// n8n displays "Today's date is <unix timestamp>"
// For example "Today's date is 1646834498755"
```

----------------------------------------

TITLE: Setting a Custom Encryption Key for n8n (Bash)
DESCRIPTION: This command sets the N8N_ENCRYPTION_KEY environment variable, which n8n uses to encrypt sensitive credentials before saving them to the database. It's essential to replace '<SOME RANDOM STRING>' with a strong, randomly generated key. This variable must be set for all workers when n8n is running in queue mode to ensure consistent encryption.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-examples/encryption-key.md#_snippet_0

LANGUAGE: bash
CODE:
```
export N8N_ENCRYPTION_KEY=<SOME RANDOM STRING>
```

----------------------------------------

TITLE: Setting N8N Encryption Key for Workers (Bash)
DESCRIPTION: This command sets the N8N_ENCRYPTION_KEY environment variable for n8n worker nodes. It is crucial for workers to have the same encryption key as the main n8n instance to decrypt and access credentials stored in the database, ensuring proper workflow execution.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/queue-mode.md#_snippet_0

LANGUAGE: Bash
CODE:
```
export N8N_ENCRYPTION_KEY=<main_instance_encryption_key>
```

----------------------------------------

TITLE: Configuring Human Message Prompt for Conversational AI Agent (LangChain Expression)
DESCRIPTION: This example demonstrates how to structure the 'Human Message' prompt for the Conversational AI Agent node in n8n. It includes placeholders for available tools, output format instructions, and the user's input, guiding the agent on how to interact and utilize tools effectively.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent/conversational-agent.md#_snippet_0

LANGUAGE: LangChain Expression
CODE:
```
TOOLS\n------\nAssistant can ask the user to use tools to look up information that may be helpful in answering the user's original question. The tools the human can use are:\n\n{tools}\n\n{format_instructions}\n\nUSER'S INPUT\n--------------------\nHere is the user's input (remember to respond with a markdown code snippet of a JSON blob with a single action, and NOTHING else):\n\n{{input}}
```

----------------------------------------

TITLE: Making HTTP Requests with Built-in Helpers (TypeScript)
DESCRIPTION: This snippet demonstrates how to use n8n's built-in `this.helpers.httpRequest` for unauthenticated requests and `this.helpers.httpRequestWithAuthentication` for authenticated requests. These helpers internally use Axios and help avoid adding external npm dependencies to your n8n node, improving performance and security.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/code-standards.md#_snippet_1

LANGUAGE: TypeScript
CODE:
```
// If no auth needed
const response = await this.helpers.httpRequest(options);

// If auth needed
const response = await this.helpers.httpRequestWithAuthentication.call(
	this, 
	'credentialTypeName', // For example: pipedriveApi
	options,
);
```

----------------------------------------

TITLE: n8n Standard Data Structure Example (JSON)
DESCRIPTION: This JSON snippet illustrates the standard data structure for data items in n8n, showing how to wrap regular data in a 'json' key and binary data in a 'binary' key. It includes required and optional properties for binary files, such as 'data', 'mimeType', 'fileExtension', and 'fileName'.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/data/data-structure.md#_snippet_0

LANGUAGE: json
CODE:
```
[
	{
		"json": {
			"apple": "beets",
			"carrot": {
				"dill": 1
			}
		},
		"binary": {
			"apple-picture": {
				"data": "....",
				"mimeType": "image/png",
				"fileExtension": "png",
				"fileName": "example.png"
			}
		}
	}
]
```

----------------------------------------

TITLE: Updating n8n using Docker Compose (Shell)
DESCRIPTION: This snippet provides the necessary shell commands to update an n8n instance managed by Docker Compose. It first pulls the latest image, then stops and removes the existing container, and finally starts a new container in detached mode with the updated image.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_snippets/self-hosting/installation/docker-compose-updating.md#_snippet_0

LANGUAGE: sh
CODE:
```
# Pull latest version
docker compose pull

# Stop and remove older version
docker compose down

# Start the container
docker compose up -d
```

----------------------------------------

TITLE: Configuring an n8n Error Workflow with Slack Notification (JSON)
DESCRIPTION: This n8n workflow configuration in JSON format defines an error handling mechanism. It starts with an 'Error Trigger' node that activates upon a monitored workflow's failure, then proceeds to a 'Slack' node to send a detailed error message, including the failed workflow's name and execution URL, to a specified Slack channel. This setup is crucial for automated error alerting.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-4.md#_snippet_0

LANGUAGE: JSON
CODE:
```
{
		"nodes": [
			{
				"parameters": {},
				"name": "Error Trigger",
				"type": "n8n-nodes-base.errorTrigger",
				"typeVersion": 1,
				"position": [
					720,
					-380
				]
			},
			{
				"parameters": {
					"channel": "channelname",
					"text": "=This workflow {{$node[\"Error Trigger\"].json[\"workflow\"][\"name\"]}}failed.\nHave a look at it here: {{$node[\"Error Trigger\"].json[\"execution\"][\"url\"]}}",
					"attachments": [],
					"otherOptions": {}
				},
				"name": "Slack",
				"type": "n8n-nodes-base.slack",
				"position": [
					900,
					-380
				],
				"typeVersion": 1,
				"credentials": {
					"slackApi": {
						"id": "17",
						"name": "slack_credentials"
					}
				}
			}
		],
		"connections": {
			"Error Trigger": {
				"main": [
					[
						{
							"node": "Slack",
							"type": "main",
							"index": 0
						}
					]
				]
			}
		}
	}
```

----------------------------------------

TITLE: Trace Linked Item in Code Node - Python
DESCRIPTION: Designed specifically for the n8n Code node, this method serves as a robust alternative to the .item property for tracing linked items. By providing a currentNodeInputIndex, it enables precise traceback from an input item to its originating item in an upstream node, facilitating complex data manipulation within code. Refer to Retrieve linked items from earlier in the workflow for an example.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/output-other-nodes.md#_snippet_13

LANGUAGE: Python
CODE:
```
_("<node-name>").itemMatching(currentNodeInputIndex)
```

----------------------------------------

TITLE: Calling $fromAI() Function for Dynamic Parameter Population - JavaScript
DESCRIPTION: This snippet illustrates the basic syntax for calling the `$fromAI()` function within an n8n expression. It uses 'email' as a 'key' parameter, which acts as a hint for the AI model to identify and populate an email-related value dynamically. This function is specifically designed for app node tools integrated with the Tools AI agent to intelligently fill in required parameters.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/advanced-ai/examples/using-the-fromai-function.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
{{ $fromAI('email') }}
```

----------------------------------------

TITLE: Exporting a Specific n8n Workflow to a File
DESCRIPTION: This command exports a single n8n workflow, identified by its ID, to a specified JSON file. Replace `<ID>` with the workflow's actual ID and `file.json` with your desired output filename.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_7

LANGUAGE: bash
CODE:
```
n8n export:workflow --id=<ID> --output=file.json
```

----------------------------------------

TITLE: Mitigating OpenAI Rate Limits with n8n Loop and Wait Nodes (JSON)
DESCRIPTION: This n8n workflow template demonstrates how to handle OpenAI rate limits by splitting input data into smaller batches using the 'Loop Over Items' node and introducing a delay with the 'Wait' node after each OpenAI API call. This helps prevent 'Too Many Requests' errors by pacing requests.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_snippets/integrations/openai-api-issues.md#_snippet_0

LANGUAGE: JSON
CODE:
```
{
    "nodes": [
    {
        "parameters": {},
        "id": "35d05920-ad75-402a-be3c-3277bff7cc67",
        "name": "When clicking ‘Execute workflow’",
        "type": "n8n-nodes-base.manualTrigger",
        "typeVersion": 1,
        "position": [
        880,
        400
        ]
    },
    {
        "parameters": {
        "batchSize": 500,
        "options": {}
        },
        "id": "ae9baa80-4cf9-4848-8953-22e1b7187bf6",
        "name": "Loop Over Items",
        "type": "n8n-nodes-base.splitInBatches",
        "typeVersion": 3,
        "position": [
        1120,
        420
        ]
    },
    {
        "parameters": {
        "resource": "chat",
        "options": {},
        "requestOptions": {}
        },
        "id": "a519f271-82dc-4f60-8cfd-533dec580acc",
        "name": "OpenAI",
        "type": "n8n-nodes-base.openAi",
        "typeVersion": 1,
        "position": [
        1380,
        440
        ]
    },
    {
        "parameters": {
        "unit": "minutes"
        },
        "id": "562d9da3-2142-49bc-9b8f-71b0af42b449",
        "name": "Wait",
        "type": "n8n-nodes-base.wait",
        "typeVersion": 1,
        "position": [
        1620,
        440
        ],
        "webhookId": "714ab157-96d1-448f-b7f5-677882b92b13"
    }
    ],
    "connections": {
    "When clicking ‘Execute workflow’": {
        "main": [
        [
            {
            "node": "Loop Over Items",
            "type": "main",
            "index": 0
            }
        ]
        ]
    },
    "Loop Over Items": {
        "main": [
        null,
        [
            {
            "node": "OpenAI",
            "type": "main",
            "index": 0
            }
        ]
        ]
    },
    "OpenAI": {
        "main": [
        [
            {
            "node": "Wait",
            "type": "main",
            "index": 0
            }
        ]
        ]
    },
    "Wait": {
        "main": [
        [
            {
            "node": "Loop Over Items",
            "type": "main",
            "index": 0
            }
        ]
        ]
    }
    },
    "pinData": {}
}
```

----------------------------------------

TITLE: n8n Workflow: Date Transformation and Conditional Processing
DESCRIPTION: This n8n workflow automates date processing. It fetches customer data, rounds the 'created' date to the end of the month, checks if the rounded date is after January 1, 1960, waits for one minute if the condition is true, and finally sets the calculated date as 'outputValue'. The workflow is configured to trigger every 30 minutes and includes a manual trigger for testing.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-2.md#_snippet_4

LANGUAGE: n8n Workflow JSON
CODE:
```
{
	"name": "Course 2, Ch 2, Date exercise",
	"nodes": [
		{
		"parameters": {},
		"id": "6bf64d5c-4b00-43cf-8439-3cbf5e5f203b",
		"name": "When clicking \"Execute workflow\"",
		"type": "n8n-nodes-base.manualTrigger",
		"typeVersion": 1,
		"position": [
			620,
			280
		]
		},
		{
		"parameters": {
			"operation": "getAllPeople",
			"returnAll": true
		},
		"id": "a08a8157-99ee-4d50-8fe4-b6d7e16e858e",
		"name": "Customer Datastore (n8n training)",
		"type": "n8n-nodes-base.n8nTrainingCustomerDatastore",
		"typeVersion": 1,
		"position": [
			840,
			360
		]
		},
		{
		"parameters": {
			"operation": "roundDate",
			"date": "={{ $json.created }}",
			"mode": "roundUp",
			"outputFieldName": "new-date",
			"options": {
			"includeInputFields": true
			}
		},
		"id": "f66a4356-2584-44b6-a4e9-1e3b5de53e71",
		"name": "Date & Time",
		"type": "n8n-nodes-base.dateTime",
		"typeVersion": 2,
		"position": [
			1080,
			360
		]
		},
		{
		"parameters": {
			"conditions": {
			"options": {
				"caseSensitive": true,
				"leftValue": "",
				"typeValidation": "strict"
			},
			"conditions": [
				{
				"id": "7c82823a-e603-4166-8866-493f643ba354",
				"leftValue": "={{ $json['new-date'] }}",
				"rightValue": "1960-01-01T00:00:00",
				"operator": {
					"type": "dateTime",
					"operation": "after"
				}
				}
			],
			"combinator": "and"
			},
			"options": {}
		},
		"id": "cea39877-6183-4ea0-9400-e80523636912",
		"name": "If"
```

----------------------------------------

TITLE: Creating n8n Credentials via REST API
DESCRIPTION: This snippet demonstrates how to programmatically create new n8n credentials using the REST API. It includes the HTTP POST request to the `/rest/credentials` endpoint, the JSON payload required for an Airtable API key, and the expected JSON response containing the ID of the newly created credential. This method is an alternative to using the n8n Editor UI for credential management.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/embed/managing-workflows.md#_snippet_0

LANGUAGE: HTTP
CODE:
```
POST https://<n8n-domain>/rest/credentials
```

LANGUAGE: JSON
CODE:
```
{
   "name":"MyAirtable",
   "type":"airtableApi",
   "nodesAccess":[
      {
         "nodeType":"n8n-nodes-base.airtable"
      }
   ],
   "data":{
      "apiKey":"q12we34r5t67yu"
   }
}
```

LANGUAGE: JSON
CODE:
```
{
   "data":{
      "name":"MyAirtable",
      "type":"airtableApi",
      "data":{
         "apiKey":"q12we34r5t67yu"
      },
      "nodesAccess":[
         {
            "nodeType":"n8n-nodes-base.airtable",
            "date":"2021-09-10T07:41:27.770Z"
         }
      ],
      "id":"29",
      "createdAt":"2021-09-10T07:41:27.777Z",
      "updatedAt":"2021-09-10T07:41:27.777Z"
   }
}
```

----------------------------------------

TITLE: Starting n8n with Docker (SQLite)
DESCRIPTION: This command initializes a Docker volume for persistent data, downloads the n8n image, and starts the container, exposing it on port 5678. It mounts the n8n_data volume to /home/<USER>/.n8n to ensure data persistence across restarts.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/docker.md#_snippet_0

LANGUAGE: sh
CODE:
```
docker volume create n8n_data

docker run -it --rm --name n8n -p 5678:5678 -v n8n_data:/home/<USER>/.n8n docker.n8n.io/n8nio/n8n
```

----------------------------------------

TITLE: Enabling n8n Execution Data Pruning (Docker Compose)
DESCRIPTION: This snippet illustrates how to enable and configure automatic data pruning for n8n within a Docker Compose setup. Environment variables are defined under the `environment` key for the `n8n` service to activate pruning, set the maximum age, and specify the maximum number of executions.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/execution-data.md#_snippet_5

LANGUAGE: yaml
CODE:
```
# Docker Compose
n8n:
    environment:
      - EXECUTIONS_DATA_PRUNE=true
      - EXECUTIONS_DATA_MAX_AGE=168
	  	- EXECUTIONS_DATA_PRUNE_MAX_COUNT=50000
```

----------------------------------------

TITLE: Defining API Call Details with `routing` and `requestDefaults` in TypeScript
DESCRIPTION: This example illustrates how `routing` is used within an `options` array to specify API call details for operations, complementing `requestDefaults`. `requestDefaults` sets up common API parameters like `baseURL` and `headers`, while `routing` provides operation-specific information such as the `method` and `url` for a particular API endpoint, as shown with a NASA API integration.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/node-base-files/declarative-style-parameters.md#_snippet_1

LANGUAGE: TypeScript
CODE:
```
description: INodeTypeDescription = {
  // Other node info here
  requestDefaults: {
			baseURL: 'https://api.nasa.gov',
			url: '',
			headers: {
				Accept: 'application/json',
				'Content-Type': 'application/json',
			},
		},
    properties: [
      // Resources here
      {
        displayName: 'Operation'
        // Other operation details
        options: [
          {
            name: 'Get'
            value: 'get',
            description: '',
            routing: {
              request: {
                method: 'GET',
                url: '/planetary/apod'
              }
            }
          }
        ]
      }
    ]
}
```

----------------------------------------

TITLE: Setting up NASA API Key Authentication in n8n (TypeScript)
DESCRIPTION: This TypeScript code defines a custom credential type, `NasaPicsApi`, for the NASA API within n8n. It includes an 'API Key' property for user input and configures generic authentication to pass the API key via a query string parameter named 'api_key'. This setup enables n8n nodes to securely authenticate with the NASA API.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/declarative-style-node.md#_snippet_8

LANGUAGE: TypeScript
CODE:
```
import {
	IAuthenticateGeneric,
	ICredentialType,
	INodeProperties,
} from 'n8n-workflow';

export class NasaPicsApi implements ICredentialType {
	name = 'NasaPicsApi';
	displayName = 'NASA Pics API';
	// Uses the link to this tutorial as an example
	// Replace with your own docs links when building your own nodes
	documentationUrl = 'https://docs.n8n.io/integrations/creating-nodes/build/declarative-style-node/';
	properties: INodeProperties[] = [
		{
			displayName: 'API Key',
			name: 'apiKey',
			type: 'string',
			default: '',
		},
	];
	authenticate = {
		type: 'generic',
		properties: {
			qs: {
				'api_key': '={{$credentials.apiKey}}'
			}
		},
	} as IAuthenticateGeneric;
}
```

----------------------------------------

TITLE: Extracting Nested Data Fields in n8n (JavaScript)
DESCRIPTION: This snippet iterates through incoming items and extracts specific nested fields, 'personal_info.first_name' and 'work_info.job_title', from each item's JSON payload. It returns a new array of items, each containing only these two extracted fields. This is useful for flattening or selecting specific data points from complex nested structures.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/ai-code.md#_snippet_3

LANGUAGE: JavaScript
CODE:
```
const items = $input.all();
const newItems = items.map((item) => {
  const firstName = item.json.personal_info.first_name;
  const jobTitle = item.json.work_info.job_title;
  return {
    json: {
      firstName,
      jobTitle,
    },
  };
});
return newItems;
```

----------------------------------------

TITLE: Merging Data with Custom SQL Query in n8n
DESCRIPTION: This SQL query demonstrates how to merge data from two previous nodes (input1 and input2) using a LEFT JOIN operation based on matching 'name' and 'id' fields. Data from previous n8n nodes are accessible as tables named input1, input2, etc., according to their order in the workflow. This allows for complex custom merge logic.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.merge.md#_snippet_0

LANGUAGE: sql
CODE:
```
SELECT * FROM input1 LEFT JOIN input2 ON input1.name = input2.id
```

----------------------------------------

TITLE: Example Input Data for Multi-Item Processing (JSON)
DESCRIPTION: This JSON snippet represents an array of two items, each with a `name-input-value` property. It illustrates the typical input format for n8n nodes when processing multiple items, demonstrating how a node configured to use item data would iterate over this structure to perform an action for each item.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_snippets/flow-logic/data-flow-nodes.md#_snippet_0

LANGUAGE: JSON
CODE:
```
[
	{
		name-input-value: "test1"
	},
	{
		name-input-value: "test2"
	}
]
```

----------------------------------------

TITLE: Resolving Undefined Returns in n8n Code Node (JavaScript)
DESCRIPTION: This error occurs when the Code node returns nothing or an unexpected 'undefined' result, indicating that the output does not conform to n8n's expected data structure. The provided snippet shows the correct format for returning data, an array of objects with a 'json' key pointing to an object. Users must ensure that all referenced data exists and matches the expected structure during execution to prevent this error.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.code/common-issues.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
[
  {
    "json": {
	  // your data goes here
	}
  }
]
```

----------------------------------------

TITLE: Summarizing Data and Creating Slack Message in n8n (JavaScript)
DESCRIPTION: This snippet processes incoming data to count ideas, features, and bugs based on their 'property_type'. It then sorts submissions by 'property_votes' to identify the top 5, and finally constructs a markdown-formatted Slack message summarizing the counts and listing the top submissions. It requires input data with 'json.property_type', 'json.url', 'json.name', and 'json.property_votes' fields.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/ai-code.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
const submissions = $input.all();

// Count the number of ideas, features, and bugs
let ideaCount = 0;
let featureCount = 0;
let bugCount = 0;

submissions.forEach((submission) => {
  switch (submission.json.property_type[0]) {
    case "Idea":
      ideaCount++;
      break;
    case "Feature":
      featureCount++;
      break;
    case "Bug":
      bugCount++;
      break;
  }
});

// Sort submissions by votes and take the top 5
const topSubmissions = submissions
  .sort((a, b) => b.json.property_votes - a.json.property_votes)
  .slice(0, 5);

let topSubmissionText = "";
topSubmissions.forEach((submission) => {
  topSubmissionText += `<${submission.json.url}|${submission.json.name}> with ${submission.json.property_votes} votes\n`;
});

// Construct the Slack message
const slackMessage = `*Summary of Submissions*\n\nIdeas: ${ideaCount}\nFeatures: ${featureCount}\nBugs: ${bugCount}\nTop 5 Submissions:\n${topSubmissionText}`;

return [{ json: { slackMessage } }];
```

----------------------------------------

TITLE: Handling 401 Unauthorized Error in n8n Gmail Node
DESCRIPTION: This snippet displays the full text of a "401 Unauthorized" error, which indicates issues with the credential's scopes or permissions when using the n8n Gmail node. It typically means the client is not authorized to retrieve access tokens or for the requested scopes. The solution involves enabling the Gmail API for OAuth2 credentials or enabling domain-wide delegation and adding the Gmail API for Service Account credentials.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.gmail/common-issues.md#_snippet_0

LANGUAGE: JSON
CODE:
```
401 - {"error":"unauthorized_client","error_description":"Client is unauthorized to retrieve access tokens using this method, or client not authorized for any of the scopes requested."}
```

----------------------------------------

TITLE: Creating Toggleable Headings in Notion via HTTP Request (n8n Expressions)
DESCRIPTION: This workaround shows how to create a toggleable heading in Notion, a feature not directly supported by the n8n Notion node. It involves creating a regular heading, then using HTTP Request nodes to retrieve the block, modify its is_toggleable property, and update the block via the Notion API using n8n expressions.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.notion/common-issues.md#_snippet_1

LANGUAGE: URL
CODE:
```
https://api.notion.com/v1/blocks/<block_ID>
```

LANGUAGE: n8n Expression
CODE:
```
https://api.notion.com/v1/blocks/{{ $json.results[0].id }}
```

LANGUAGE: n8n Expression
CODE:
```
https://api.notion.com/v1/blocks/{{ $json.id }}
```

LANGUAGE: n8n Expression
CODE:
```
{{ $json.heading_1 }}
```

----------------------------------------

TITLE: Configuring n8n with PostgreSQL in Docker
DESCRIPTION: This command starts n8n in a Docker container, configuring it to use PostgreSQL as its database. It sets environment variables for the PostgreSQL database type, host, port, user, schema, and password, while also persisting n8n's user data and encryption key via a Docker volume.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/docker.md#_snippet_1

LANGUAGE: sh
CODE:
```
docker volume create n8n_data

docker run -it --rm \
 --name n8n \
 -p 5678:5678 \
 -e DB_TYPE=postgresdb \
 -e DB_POSTGRESDB_DATABASE=<POSTGRES_DATABASE> \
 -e DB_POSTGRESDB_HOST=<POSTGRES_HOST> \
 -e DB_POSTGRESDB_PORT=<POSTGRES_PORT> \
 -e DB_POSTGRESDB_USER=<POSTGRES_USER> \
 -e DB_POSTGRESDB_SCHEMA=<POSTGRES_SCHEMA> \
 -e DB_POSTGRESDB_PASSWORD=<POSTGRES_PASSWORD> \
 -v n8n_data:/home/<USER>/.n8n \
 docker.n8n.io/n8nio/n8n
```

----------------------------------------

TITLE: Dynamically Populating SQL IN Clause with n8n Parameters
DESCRIPTION: This advanced SQL snippet, combined with an n8n expression, demonstrates how to dynamically generate indexed placeholders (`$1`, `$2`, etc.) for a Postgres `IN` clause. It allows n8n to automatically create prepared statement placeholders based on the size of an input array, ensuring safe and flexible queries.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.postgres/common-issues.md#_snippet_2

LANGUAGE: SQL
CODE:
```
SELECT color, shirt_size FROM shirts WHERE shirt_size IN ({{ $json.input_shirt_sizes.map((i, pos) => "$" + (pos+1)).join(', ') }});
```

----------------------------------------

TITLE: Starting n8n Docker Container with Tunnel (Shell)
DESCRIPTION: This set of commands initializes a Docker volume for n8n data and then starts the n8n container with the `--tunnel` option. The tunnel option allows n8n to be accessible from outside the local network, useful for testing webhooks. It maps port 5678 and mounts the created volume for persistent data.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/docker.md#_snippet_6

LANGUAGE: sh
CODE:
```
docker volume create n8n_data

docker run -it --rm \
 --name n8n \
 -p 5678:5678 \
 -v n8n_data:/home/<USER>/.n8n \
 docker.n8n.io/n8nio/n8n \
 start --tunnel
```

----------------------------------------

TITLE: Automating N8N Source Control Pull with cURL
DESCRIPTION: This cURL command automates the process of pulling source control changes into an n8n instance. It sends a POST request to the /api/v1/source-control/pull endpoint, typically after a Git merge, to synchronize workflows. The force: true data parameter ensures that the pull operation proceeds even if there are local uncommitted changes, making it suitable for CI/CD environments.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/source-control-environments/using/copy-work.md#_snippet_0

LANGUAGE: curl
CODE:
```
curl --request POST \
	--location '<YOUR-INSTANCE-URL>/api/v1/source-control/pull' \
	--header 'Content-Type: application/json' \
	--header 'X-N8N-API-KEY: <YOUR-API-KEY>' \
	--data '{"force": true}'
```

----------------------------------------

TITLE: Perform JMESPath Search with _jmespath() in Python
DESCRIPTION: The `_jmespath()` method provides n8n users with the capability to perform JMESPath searches on JSON objects when writing Python code. This method is accessible within the n8n Code node, facilitating advanced data manipulation and querying.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/jmespath.md#_snippet_1

LANGUAGE: Python
CODE:
```
_jmespath()
```

----------------------------------------

TITLE: Exporting All n8n Workflows to a Directory for Backup
DESCRIPTION: This command exports all n8n workflows to a specified directory using the `--backup` flag, which automatically enables `--all`, `--pretty`, and `--separate` for organized backups. The `--output` flag specifies the target directory.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_9

LANGUAGE: bash
CODE:
```
n8n export:workflow --backup --output=backups/latest/
```

----------------------------------------

TITLE: Activating n8n Workflow via REST API (PATCH)
DESCRIPTION: This snippet shows how to activate an existing n8n workflow using a PATCH request. The workflow ID (e.g., `1012`) is part of the URL, and the request body should include `{"active": true}`.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/embed/managing-workflows.md#_snippet_5

LANGUAGE: HTTP
CODE:
```
PATCH https://<n8n-domain>/rest/workflows/1012
```

----------------------------------------

TITLE: Managing Global Workflow Static Data in JavaScript
DESCRIPTION: This snippet demonstrates how to retrieve, access, update, and delete global static data within an n8n workflow using JavaScript. Global static data is accessible by all nodes in the workflow and is automatically saved if changed upon successful workflow execution. It's suitable for small, persistent data like timestamps.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/builtin/get-workflow-static-data.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
// Get the global workflow static data
const workflowStaticData = $getWorkflowStaticData('global');

// Access its data
const lastExecution = workflowStaticData.lastExecution;

// Update its data
workflowStaticData.lastExecution = new Date().getTime();

// Delete data
delete workflowStaticData.lastExecution;
```

----------------------------------------

TITLE: Granting Full Access to AWS Secrets Manager for n8n (IAM Policy)
DESCRIPTION: This IAM policy grants an n8n-associated IAM user comprehensive permissions to list, retrieve, and describe all secrets within AWS Secrets Manager. It includes actions like `secretsmanager:ListSecrets`, `secretsmanager:BatchGetSecretValue`, `secretsmanager:GetSecretValue`, and `secretsmanager:DescribeSecret` across all resources (`"Resource": "*"`). This policy is suitable for scenarios where n8n requires broad access to all secrets in the account.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/external-secrets.md#_snippet_0

LANGUAGE: JSON
CODE:
```
{
	"Version": "2012-10-17",
	"Statement": [
		{
			"Sid": "AccessAllSecrets",
			"Effect": "Allow",
			"Action": [
				"secretsmanager:ListSecrets",
				"secretsmanager:BatchGetSecretValue",
 				"secretsmanager:GetResourcePolicy",
				"secretsmanager:GetSecretValue",
				"secretsmanager:DescribeSecret",
				"secretsmanager:ListSecretVersionIds"
			],
			"Resource": "*"
		}
	]
}
```

----------------------------------------

TITLE: Referencing Chat Input for Sentiment Analysis (n8n Expression)
DESCRIPTION: This expression is used in the 'Text to Analyze' field to specify that the input text for sentiment analysis should come from a 'chatInput' field within the incoming JSON data. It's typically used when the data originates from a chat or message source.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.sentimentanalysis.md#_snippet_0

LANGUAGE: n8n Expression
CODE:
```
{{ $json.chatInput }}
```

----------------------------------------

TITLE: Transforming Single Item to Multiple Items in n8n Code Node (JavaScript)
DESCRIPTION: This JavaScript snippet demonstrates how to transform a single n8n item containing an array of objects under a `data` key into multiple individual n8n items. It uses the `map` function to iterate over the `data` array and create a new item for each element.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-1.md#_snippet_5

LANGUAGE: JavaScript
CODE:
```
return $input.first().json.data.map(item => {
    return {
        json: item
    }
});
```

----------------------------------------

TITLE: Accessing Linked Item from Previous Node Output in n8n Expressions (JavaScript)
DESCRIPTION: This expression retrieves the linked parent item from a specified previous node's output. n8n traces the item linking chain backward to find the relevant parent item. It returns the entire linked item object.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/data/data-mapping/data-mapping-expressions.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
{{$("<node-name>").item}}
```

----------------------------------------

TITLE: Accessing Order Status with n8n Expression
DESCRIPTION: This expression is used within the n8n If node to dynamically retrieve the `orderStatus` value from the incoming JSON data, typically from a preceding HTTP Request node. It allows the If node to evaluate the status and route the workflow accordingly.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-one/chapter-5/chapter-5.3.md#_snippet_0

LANGUAGE: n8n Expression
CODE:
```
{{ $json.orderStatus }}
```

----------------------------------------

TITLE: Extracting City from Webhook Body (JavaScript)
DESCRIPTION: This JavaScript expression demonstrates how to access the 'city' value from the incoming webhook body using n8n's custom `$json` variable and JMESPath-like syntax. It directly retrieves data from the `body` property of the `$json` object, returning 'New York' in this example.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/expressions.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
{{$json.body.city}}
```

----------------------------------------

TITLE: Updating MySQL Rows with Composite Key - SQL
DESCRIPTION: This SQL snippet demonstrates how to update rows in a MySQL table that uses a composite key. Since the standard 'Update' operation in n8n's MySQL node only supports single-column matching, a manual 'Execute SQL' operation is required to match on multiple columns (e.g., customer_id and product_id) to uniquely identify and update specific rows.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.mysql/common-issues.md#_snippet_0

LANGUAGE: SQL
CODE:
```
UPDATE orders SET quantity = 3 WHERE customer_id = 538 AND product_id = 800;
```

----------------------------------------

TITLE: Configuring Webhook URL (Environment Variable)
DESCRIPTION: This command sets the WEBHOOK_URL environment variable, which defines the public URL for your n8n webhooks. This is crucial for n8n to correctly generate and respond to webhook requests, especially when behind a load balancer or proxy.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/queue-mode.md#_snippet_7

LANGUAGE: bash
CODE:
```
export WEBHOOK_URL=https://your-webhook-url.com
```

----------------------------------------

TITLE: Custom Formatting Dates for Human Readability in n8n (JavaScript)
DESCRIPTION: This snippet demonstrates how to apply a custom human-readable format to a Luxon DateTime object using `toLocaleString()` with an options object. This allows specifying desired components like month, day, and year (e.g., 'long', 'numeric') to achieve a precise output format like '16 June 2019'.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/luxon.md#_snippet_8

LANGUAGE: JavaScript
CODE:
```
{{$today.minus({days: 7}).toLocaleString({month: 'long', day: 'numeric', year: 'numeric'})}}
```

LANGUAGE: JavaScript
CODE:
```
let readableSevenDaysAgo = $today.minus({days: 7}).toLocaleString({month: 'long', day: 'numeric', year: 'numeric'})
```

----------------------------------------

TITLE: Parameterized SELECT Query for MySQL - SQL
DESCRIPTION: This SQL snippet demonstrates a parameterized SELECT query designed for use with n8n's MySQL node. It uses `$1:name` for a dynamic table name and `$2` for a dynamic email value, allowing for safe and flexible data retrieval while preventing SQL injection.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.mysql/index.md#_snippet_1

LANGUAGE: sql
CODE:
```
SELECT * FROM $1:name WHERE email = $2;
```

----------------------------------------

TITLE: Configuring MongoDB Atlas Vector Search Index (JSON)
DESCRIPTION: This JSON snippet provides an example configuration for creating a Vector Search index in MongoDB Atlas. It defines a 'vector' type field, specifying its 'path', 'numDimensions' (e.g., 1536 for OpenAI embeddings), and 'similarity' function. This index is a prerequisite for using the n8n MongoDB Atlas Vector Store node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.vectorstoremongodbatlas.md#_snippet_0

LANGUAGE: JSON
CODE:
```
{
  "fields": [
    {
      "type": "vector",
      "path": "<field-name>",
      "numDimensions": 1536, // any other value
      "similarity": "<similarity-function>"
    }
  ]
}
```

----------------------------------------

TITLE: Exposing Local Server with ngrok (Shell)
DESCRIPTION: This shell command initiates ngrok to create a secure tunnel from the internet to a local server running on port 5678. It's used to expose a local development environment to the public internet, which is necessary for services like GetResponse that do not accept localhost as a redirect URL for OAuth2.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/getresponse.md#_snippet_0

LANGUAGE: sh
CODE:
```
ngrok http 5678
```

----------------------------------------

TITLE: Retrieve All Items from Node - Python
DESCRIPTION: These methods retrieve data items from a specified upstream node. They allow for optional branchIndex and runIndex parameters to target specific outputs or workflow runs. When branchIndex is omitted, the method defaults to the output directly connecting the source node to the current node, making them suitable for use within the n8n Code node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/output-other-nodes.md#_snippet_7

LANGUAGE: Python
CODE:
```
_("<node-name>").all(branchIndex?, runIndex?)
```

----------------------------------------

TITLE: Extracting First and Last Names in Code Node (Python)
DESCRIPTION: This Python code snippet shows how to use the `_jmespath` function within an n8n Code node to extract 'first' and 'last' names from the 'people' array. The extracted data is then returned as a dictionary.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/jmespath.md#_snippet_10

LANGUAGE: python
CODE:
```
newList = _jmespath(_json.body.people, "[].[first, last]")
return {"newList":newList}
```

----------------------------------------

TITLE: Ensuring Proper Data Return Format in n8n Code Node (JavaScript)
DESCRIPTION: This error occurs when the Code node does not return data in the expected n8n format, which requires an array of objects, each containing a 'json' key pointing to an object. The snippet demonstrates the correct structure for outputting data from the Code node, ensuring compatibility with subsequent nodes in the workflow. Users should verify their code adheres to this structure to prevent data parsing issues.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.code/common-issues.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
[
  {
    "json": {
	  // your data goes here
	}
  }
]
```

----------------------------------------

TITLE: Using Nullish Coalescing Operator for Default Values in n8n
DESCRIPTION: This snippet illustrates the use of the nullish coalescing operator (??) in an n8n expression. It provides a concise way to assign a 'default value' if the variable `$x` is strictly `null` or `undefined`. If `$x` has any other value (including `false` or `0`), that value is used instead.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/expressions/check-incoming-data.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
{{ $x ?? "default value" }}
```

----------------------------------------

TITLE: Returning Data from n8n Code Node
DESCRIPTION: This JavaScript snippet shows the required structure for returning data from an n8n Code node. It demonstrates wrapping the actual data object (e.g., { apple: 'beets' }) within a 'json' key inside an array, which is the expected format for n8n items.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-1.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
return [
	{
		json: {
			apple: 'beets',
		}
	}
];
```

----------------------------------------

TITLE: Creating n8n Node Declaratively (JavaScript)
DESCRIPTION: This JavaScript snippet illustrates the declarative style for building an n8n node. Unlike the programmatic approach, it omits the explicit `execute` method. Instead, it configures API interactions directly within the `INodeTypeDescription` using `requestDefaults` for base URL and `routing` objects within operation options to define HTTP methods, URLs, and data sending/receiving logic. It requires `INodeType` and `INodeTypeDescription` from `n8n-workflow`.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/plan/choose-node-method.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
import { INodeType, INodeTypeDescription } from 'n8n-workflow';

// Create the FriendGrid class
export class FriendGrid implements INodeType {
  description: INodeTypeDescription = {
    displayName: 'FriendGrid',
    name: 'friendGrid',
    . . .
    // Set up the basic request configuration
    requestDefaults: {
      baseURL: 'https://api.sendgrid.com/v3/marketing'
    },
    properties: [
      {
        displayName: 'Resource',
        . . .
      },
      {
        displayName: 'Operation',
        name: 'operation',
        type: 'options',
        displayOptions: {
          show: {
            resource: [
              'contact',
            ],
          },
        },
        options: [
          {
            name: 'Create',
            value: 'create',
            description: 'Create a contact',
            // Add the routing object
            routing: {
              request: {
                method: 'POST',
                url: '=/contacts',
                send: {
                  type: 'body',
                  properties: {
                    email: {{$parameter["email"]}}
                  }
                }
              }
            },
            // Handle the response to contact creation
            output: {
              postReceive: [
                {
                  type: 'set',
                  properties: {
                    value: '={{ { "success": $response } }}'
                  }
                }
              ]
            }
          },
        ],
        default: 'create',
        description: 'The operation to perform.',
      },
      {
        displayName: 'Email',
        . . .
      },
      {
        displayName: 'Additional Fields',
        // Sets up optional fields
      },
    ],
  }
  // No execute method needed
}
```

----------------------------------------

TITLE: Outline Structure for a Programmatic-style n8n Node (JavaScript)
DESCRIPTION: This snippet illustrates the fundamental class structure for a programmatic-style n8n node. It imports `IExecuteFunctions` from `n8n-core` and `INodeExecutionData`, `INodeType`, `INodeTypeDescription` from `n8n-workflow`. In addition to the `description` object, it includes an `async execute()` method, which is responsible for processing incoming data and parameters and returning the results.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/node-base-files/structure.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
import { IExecuteFunctions } from 'n8n-core';
import { INodeExecutionData, INodeType, INodeTypeDescription } from 'n8n-workflow';

export class ExampleNode implements INodeType {
	description: INodeTypeDescription = {
    // Basic node details here
    properties: [
      // Resources and operations here
    ]
  };

  async execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
    // Process data and return
  }
};
```

----------------------------------------

TITLE: Managing Global Workflow Static Data in Python
DESCRIPTION: This snippet illustrates how to retrieve, access, update, and delete global static data within an n8n workflow using Python. Global static data is shared across all nodes and is automatically persisted if modified upon successful workflow execution. It's ideal for storing small, persistent values such as timestamps.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/builtin/get-workflow-static-data.md#_snippet_1

LANGUAGE: Python
CODE:
```
# Get the global workflow static data
workflowStaticData = _getWorkflowStaticData('global')

# Access its data
lastExecution = workflowStaticData.lastExecution

# Update its data
workflowStaticData.lastExecution = new Date().getTime()

# Delete data
delete workflowStaticData.lastExecution
```

----------------------------------------

TITLE: Installing n8n globally with npm (Bash)
DESCRIPTION: This command installs the latest stable version of n8n globally on the system, making it accessible from any directory in the terminal. It requires Node.js 18 or above.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/npm.md#_snippet_1

LANGUAGE: bash
CODE:
```
npm install n8n -g
```

----------------------------------------

TITLE: Accessing Binary Data Buffer with Parameters (JavaScript)
DESCRIPTION: This snippet demonstrates how to use the `getBinaryDataBuffer()` function to retrieve a binary data buffer. It requires `itemIndex` (the index of the input item) and `binaryPropertyName` (the name of the binary property, typically 'data') as parameters. This function is essential for operations on binary file data within n8n workflows.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/code-node/get-binary-data-buffer.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
/* 
* itemIndex: number. The index of the item in the input data.
* binaryPropertyName: string. The name of the binary property. 
* The default in the Read/Write File From Disk node is 'data'. 
*/
let binaryDataBufferItem = await this.helpers.getBinaryDataBuffer(itemIndex, binaryPropertyName);
```

----------------------------------------

TITLE: n8n Workflow for Batch RSS Feed Processing (JSON)
DESCRIPTION: This JSON represents a complete n8n workflow that demonstrates how to read multiple RSS feeds in batches. It includes a Manual Trigger, a Code node to define the RSS feed URLs, a Loop Over Items node to process each URL individually, and an RSS Read node to fetch the content.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-3.md#_snippet_2

LANGUAGE: JSON
CODE:
```
{
	"meta": {
		"templateCredsSetupCompleted": true,
		"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"
	},
	"nodes": [
		{
		"parameters": {},
		"id": "ed8dc090-ae8c-4db6-a93b-0fa873015c25",
		"name": "When clicking \"Execute workflow\"",
		"type": "n8n-nodes-base.manualTrigger",
		"typeVersion": 1,
		"position": [
			460,
			460
		]
		},
		{
		"parameters": {
			"jsCode": "let urls = [\n  {\n    json: {\n      url: 'https://medium.com/feed/n8n-io'\n    }\n  },\n  {\n   json: {\n     url: 'https://dev.to/feed/n8n'\n   } \n  }\n]\n\nreturn urls;"
		},
		"id": "1df2a9bf-f970-4e04-b906-92dbbc9e8d3a",
		"name": "Code",
		"type": "n8n-nodes-base.code",
		"typeVersion": 2,
		"position": [
			680,
			460
		]
		},
		{
		"parameters": {
			"options": {}
		},
		"id": "3cce249a-0eab-42e2-90e3-dbdf3684e012",
		"name": "Loop Over Items",
		"type": "n8n-nodes-base.splitInBatches",
		"typeVersion": 3,
		"position": [
			900,
			460
		]
		},
		{
		"parameters": {
			"url": "={{ $json.url }}",
			"options": {}
		},
		"id": "50e1c1dc-9a5d-42d3-b7c0-accc31636aa6",
		"name": "RSS Read",
		"type": "n8n-nodes-base.rssFeedRead",
		"typeVersion": 1,
		"position": [
			1120,
			460
		]
		}
	],
	"connections": {
		"When clicking \"Execute workflow\"": {
		"main": [
			[
			{
				"node": "Code",
				"type": "main",
				"index": 0
			}
			]
		]
		},
		"Code": {
		"main": [
			[
			{
				"node": "Loop Over Items",
				"type": "main",
				"index": 0
			}
			]
		]
		},
		"Loop Over Items": {
		"main": [
			null,
			[
			{
				"node": "RSS Read",
				"type": "main",
				"index": 0
			}
			]
		]
		},
		"RSS Read": {
		"main": [
			[
			{
				"node": "Loop Over Items",
				"type": "main",
				"index": 0
			}
			]
		]
		}
	},
	"pinData": {}
}
```

----------------------------------------

TITLE: Setting Default Form Field Values with Query Parameters - URL
DESCRIPTION: This URL demonstrates how to pre-populate form fields ('email' and 'name') using query parameters. Special characters like '@' and spaces are percent-encoded ('%40' and '%20' respectively) to ensure proper parsing by the n8n Form Trigger. This method is applicable for setting initial values on any page of a multi-step form and is only available in production mode.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.form.md#_snippet_0

LANGUAGE: Text
CODE:
```
https://my-account.n8n.cloud/form/my-form?email=jane.doe%40example.com&name=Jane%20Doe
```

----------------------------------------

TITLE: Installing Docker and Docker Compose on Ubuntu
DESCRIPTION: This snippet provides a Bash script to remove old Docker installations, install prerequisites, download the Docker GPG key, configure the Docker APT repository, and finally install docker-ce, docker-ce-cli, containerd.io, docker-buildx-plugin, and docker-compose-plugin on Ubuntu.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/docker-compose.md#_snippet_0

LANGUAGE: bash
CODE:
```
# Remove incompatible or out of date Docker implementations if they exist
for pkg in docker.io docker-doc docker-compose docker-compose-v2 podman-docker containerd runc; do sudo apt-get remove $pkg; done
# Install prereq packages
sudo apt-get update
sudo apt-get install ca-certificates curl
# Download the repo signing key
sudo install -m 0755 -d /etc/apt/keyrings
sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
sudo chmod a+r /etc/apt/keyrings/docker.asc
# Configure the repository
echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $(. /etc/os-release && echo "${UBUNTU_CODENAME:-$VERSION_CODENAME}") stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Update and install Docker and Docker Compose
sudo apt-get update
sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
```

----------------------------------------

TITLE: Retrieve First Item from Node - Python
DESCRIPTION: These methods retrieve data items from a specified upstream node. They allow for optional branchIndex and runIndex parameters to target specific outputs or workflow runs. When branchIndex is omitted, the method defaults to the output directly connecting the source node to the current node, making them suitable for use within the n8n Code node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/output-other-nodes.md#_snippet_8

LANGUAGE: Python
CODE:
```
_("<node-name>").first(branchIndex?, runIndex?)
```

----------------------------------------

TITLE: Integrating AI-Generated Value into a String Expression - JavaScript
DESCRIPTION: Demonstrates how to embed the `$fromAI()` expression within a larger string to partially populate a field. This example prefixes an AI-generated email subject with 'Generated by AI:'.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/advanced-ai/examples/using-the-fromai-function.md#_snippet_4

LANGUAGE: javascript
CODE:
```
Generated by AI: {{ $fromAI("subject") }}
```

----------------------------------------

TITLE: Applying All Kubernetes Manifests (Shell)
DESCRIPTION: This shell command uses `kubectl apply -f .` to send all Kubernetes manifest files in the current directory to the cluster. This command is used to create or update the defined resources, such as deployments, services, and volumes, within the Kubernetes environment.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/google-cloud.md#_snippet_5

LANGUAGE: shell
CODE:
```
kubectl apply -f .
```

----------------------------------------

TITLE: Extracting Next Page URL from HTTP Response (n8n Expression)
DESCRIPTION: This snippet demonstrates how to extract the URL for the next page directly from the API's response body. It uses an n8n expression to access a specific property, 'next-page', within the $response.body object, which is then used by the HTTP Request node to fetch subsequent pages.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/http-node/pagination.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
{{ $response.body["next-page"] }}
```

----------------------------------------

TITLE: Applying All Kubernetes Manifests
DESCRIPTION: This command applies all Kubernetes manifest files (e.g., deployments, services) present in the current directory to the cluster. It's used to create or update the n8n and Postgres applications and their associated resources.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/aws.md#_snippet_6

LANGUAGE: shell
CODE:
```
kubectl apply -f .
```

----------------------------------------

TITLE: Mounting n8n Persistent Volume Claim in Deployment (YAML)
DESCRIPTION: This YAML snippet illustrates how a persistent volume claim named n8n-claim0 is mounted within the volumes section of the n8n-deployment.yaml manifest. This setup is crucial for persisting file-related data, such as those used by binary data nodes or manual encryption keys, across n8n pod restarts.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/azure.md#_snippet_2

LANGUAGE: yaml
CODE:
```
…
volumes:
  - name: n8n-claim0
    persistentVolumeClaim:
      claimName: n8n-claim0
…
```

----------------------------------------

TITLE: Incrementing Page Number for Pagination (n8n Expression)
DESCRIPTION: This snippet shows how to paginate by incrementing a page number parameter. It utilizes the built-in n8n variable $pageCount, which tracks the number of pages already fetched, adding +1 to it to request the next sequential page, assuming APIs typically start page counts from one.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/http-node/pagination.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
{{ $pageCount + 1 }}
```

----------------------------------------

TITLE: HTTP Request Options Object Structure (TypeScript)
DESCRIPTION: This snippet defines the structure of the `options` object used by n8n's HTTP request helpers. It lists various configurable properties such as `url` (required), `headers`, `method`, `body` (supporting different data types), `qs` for query parameters, `arrayFormat` for array serialization, `auth` for basic authentication, and other control flags like `disableFollowRedirect`, `skipSslCertificateValidation`, and `returnFullResponse`. It also includes options for `proxy` configuration and `timeout`.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/http-helpers.md#_snippet_1

LANGUAGE: TypeScript
CODE:
```
{
	url: string;
	headers?: object;
	method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'HEAD';
	body?: FormData | Array | string | number | object | Buffer | URLSearchParams;
	qs?: object;
	arrayFormat?: 'indices' | 'brackets' | 'repeat' | 'comma';
	auth?: {
		username: string,
		password: string,
	};
	disableFollowRedirect?: boolean;
	encoding?: 'arraybuffer' | 'blob' | 'document' | 'json' | 'text' | 'stream';
	skipSslCertificateValidation?: boolean;
	returnFullResponse?: boolean;
	proxy?: {
		host: string;
		port: string | number;
		auth?: {
			username: string;
			password: string;
		},
		protocol?: string;
	};
	timeout?: number;
	json?: boolean;
}
```

----------------------------------------

TITLE: Correcting 'json' Property Type in n8n Code Node (JavaScript)
DESCRIPTION: This issue arises when the 'json' key within the Code node's return data is set to a non-object data structure, such as an array. The first code example illustrates the incorrect assignment that causes the error, while the second provides the correct implementation, ensuring the 'json' key always references an object as required by n8n's data structure. This is crucial for proper data handling between nodes.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.code/common-issues.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
[
  {
    "json": [
	  // Setting `json` to an array like this will produce an error
	]
  }
]
```

LANGUAGE: JavaScript
CODE:
```
[
  {
    "json": {
	  // Setting `json` to an object as expected
	}
  }
]
```

----------------------------------------

TITLE: Replacing ES Modules with CommonJS in n8n Code Node (JavaScript)
DESCRIPTION: This error indicates an attempt to use ES module syntax (`import`, `export`) which is not supported in n8n's JavaScript sandbox. The snippet demonstrates how to resolve this by replacing `import` statements with the CommonJS `require` function, allowing modules to be loaded correctly. This adjustment is necessary for external module integration within the n8n environment.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.code/common-issues.md#_snippet_3

LANGUAGE: JavaScript
CODE:
```
// Original code:
// import express from "express";
// New code:
const express = require("express");
```

----------------------------------------

TITLE: Running n8n with Custom Certificates using Docker CLI
DESCRIPTION: This command runs the n8n Docker container, mapping the local 'pki' directory containing custom certificates to '/opt/custom-certificates' inside the container. This allows n8n to trust self-signed or custom CA certificates for secure connections.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-examples/custom-certificate-authority.md#_snippet_0

LANGUAGE: bash
CODE:
```
docker run -it --rm \
 --name n8n \
 -p 5678:5678 \
 -v ./pki:/opt/custom-certificates \
 docker.n8n.io/n8nio/n8n
```

----------------------------------------

TITLE: Opening Port 443 in Droplet Firewall (Shell)
DESCRIPTION: This command configures the Uncomplicated Firewall (UFW) to allow incoming traffic on port 443. This port is used for secure HTTPS communication, which is crucial for n8n to operate securely with SSL/TLS certificates managed by Caddy.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/digital-ocean.md#_snippet_7

LANGUAGE: shell
CODE:
```
sudo ufw allow 443
```

----------------------------------------

TITLE: Extracting Dog Ages with JMESPath Object Projection
DESCRIPTION: This snippet demonstrates how to use JMESPath object projections to extract a list of 'age' values from the 'dogs' object. It shows implementation in both n8n expressions and Code nodes for JavaScript and Python, returning an array of numbers.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/jmespath.md#_snippet_6

LANGUAGE: JavaScript
CODE:
```
{{$jmespath($json.body.dogs, "*.age")}}
// Returns [7,5]
```

LANGUAGE: JavaScript
CODE:
```
let dogsAges = $jmespath($json.body.dogs, "*.age");
return {dogsAges};
/* Returns:
[
	{
		"dogsAges": [
			7,
			5
		]
	}
]
*/
```

LANGUAGE: Python
CODE:
```
dogsAges = _jmespath(_json.body.dogs, "*.age")
return {"dogsAges": dogsAges}
"""
Returns:
[
	{
		"dogsAges": [
			7,
			5
		]
	}
]
"""
```

----------------------------------------

TITLE: Example n8n Queue Metrics Output (Prometheus Format)
DESCRIPTION: This snippet presents an example of the Prometheus-formatted output for n8n's queue metrics. It illustrates various metrics related to job processing, such as active, completed, failed, and waiting jobs, along with their types (gauge, counter) and descriptive help text. These metrics become available when `N8N_METRICS_INCLUDE_QUEUE_METRICS` is enabled.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-examples/prometheus.md#_snippet_1

LANGUAGE: plaintext
CODE:
```
# HELP n8n_scaling_mode_queue_jobs_active Current number of jobs being processed across all workers in scaling mode.
# TYPE n8n_scaling_mode_queue_jobs_active gauge
n8n_scaling_mode_queue_jobs_active 0

# HELP n8n_scaling_mode_queue_jobs_completed Total number of jobs completed across all workers in scaling mode since instance start.
# TYPE n8n_scaling_mode_queue_jobs_completed counter
n8n_scaling_mode_queue_jobs_completed 0

# HELP n8n_scaling_mode_queue_jobs_failed Total number of jobs failed across all workers in scaling mode since instance start.
# TYPE n8n_scaling_mode_queue_jobs_failed counter
n8n_scaling_mode_queue_jobs_failed 0

# HELP n8n_scaling_mode_queue_jobs_waiting Current number of enqueued jobs waiting for pickup in scaling mode.
# TYPE n8n_scaling_mode_queue_jobs_waiting gauge
n8n_scaling_mode_queue_jobs_waiting 0
```

----------------------------------------

TITLE: Finding Data Across Datasets in n8n Code Node - JavaScript
DESCRIPTION: This JavaScript snippet demonstrates how to find a specific Notion user's ID based on a Slack user's email. It accesses data from two different nodes ('Mock Slack' and the current input) and handles cases where the email property might be null. The output is an array containing an object with the found 'notionId' or an empty array if not found.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/ai-code.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
const slackUser = $("Mock Slack").all()[0];
const notionUsers = $input.all();
const slackUserEmail = slackUser.json.email;

const notionUser = notionUsers.find(
  (user) => user.json.person && user.json.person.email === slackUserEmail
);

return notionUser ? [{ json: { notionId: notionUser.json.id } }] : [];
```

----------------------------------------

TITLE: Configuring n8n with Custom Certificates using Docker Compose
DESCRIPTION: This Docker Compose configuration defines an n8n service that mounts the local 'pki' directory into the container at '/opt/custom-certificates'. This enables n8n to recognize and trust custom certificate authorities or self-signed certificates for its outgoing connections.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-examples/custom-certificate-authority.md#_snippet_1

LANGUAGE: yaml
CODE:
```
name: n8n
services:
    n8n:
        volumes:
            - ./pki:/opt/custom-certificates
        container_name: n8n
        ports:
            - 5678:5678
        image: docker.n8n.io/n8nio/n8n
```

----------------------------------------

TITLE: Managing n8n Docker Container Lifecycle (Shell)
DESCRIPTION: These commands provide a sequence for stopping, removing, and restarting an n8n Docker container after an image update. It requires the container ID, which can be found using `docker ps -a`. The final command starts a new container, allowing for custom naming and options.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/docker.md#_snippet_5

LANGUAGE: sh
CODE:
```
# Find your container ID
docker ps -a

# Stop the container with the `<container_id>`
docker stop <container_id>

# Remove the container with the `<container_id>`
docker rm <container_id>

# Start the container
docker run --name=<container_name> [options] -d docker.n8n.io/n8nio/n8n
```

----------------------------------------

TITLE: Defining a Simple JSON Object
DESCRIPTION: This snippet demonstrates a basic JSON object with two key-value pairs: 'name' and 'color'. It illustrates how properties are stored and accessed by key name, contrasting with array indexing.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-1.md#_snippet_0

LANGUAGE: JSON
CODE:
```
{
	name: 'Michelangelo',
	color: 'blue',
}
```

----------------------------------------

TITLE: Installing a specific n8n version with npm (Bash)
DESCRIPTION: This command installs or updates n8n to a precise version specified by the '@' syntax. It's useful for maintaining compatibility or reverting to a known stable release.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/npm.md#_snippet_2

LANGUAGE: bash
CODE:
```
npm install -g n8n@0.126.1
```

----------------------------------------

TITLE: Calculating Total Booked Orders and Value in n8n Code Node (JavaScript)
DESCRIPTION: This JavaScript code snippet, designed for an n8n Code node in 'Run Once for All Items' mode, calculates the total number of input items and the sum of their 'orderPrice' values. It iterates through all input items, accesses their 'json' property, and aggregates the data. The results are then returned in the required n8n data structure.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-one/chapter-5/chapter-5.5.md#_snippet_1

LANGUAGE: javascript
CODE:
```
let items = $input.all();
let totalBooked = items.length;
let bookedSum = 0;

for (let i=0; i < items.length; i++) {
  bookedSum = bookedSum + items[i].json.orderPrice;
}

return [{ json: {totalBooked, bookedSum} }];
```

----------------------------------------

TITLE: Transforming Specific Field with Multiple Values to Multiple Items in n8n Code Node (JavaScript)
DESCRIPTION: This snippet shows how to transform a specific field within an n8n item that contains an array of values into multiple individual n8n items. It accesses the `workEmail` field of the first input item and maps each value in its array to a new output item.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-1.md#_snippet_7

LANGUAGE: JavaScript
CODE:
```
let items = $input.all();
return items[0].json.workEmail.map(item => {
	return {
		json: item
	}
});
```

----------------------------------------

TITLE: Managing Node-Specific Static Data in Python
DESCRIPTION: This snippet demonstrates how to retrieve, access, update, and delete static data unique to a specific node in an n8n workflow using Python. Node static data is only accessible by the node that set it and is automatically persisted if modified upon successful workflow execution. It's suitable for small, node-specific persistent values.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/builtin/get-workflow-static-data.md#_snippet_3

LANGUAGE: Python
CODE:
```
# Get the static data of the node
nodeStaticData = _getWorkflowStaticData('node')

# Access its data
lastExecution = nodeStaticData.lastExecution

# Update its data
nodeStaticData.lastExecution = new Date().getTime()

# Delete data
delete nodeStaticData.lastExecution
```

----------------------------------------

TITLE: Configuring Postgres Persistent Storage Topology (YAML)
DESCRIPTION: Defines the allowed topologies for the Kubernetes Storage Class used by Postgres persistent storage. This snippet shows how to specify the Google Cloud zones where the storage can be created, defaulting to 'us-central1-b' and 'us-central1-c'.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/google-cloud.md#_snippet_2

LANGUAGE: yaml
CODE:
```
…
allowedTopologies:
  - matchLabelExpressions:
      - key: failure-domain.beta.kubernetes.io/zone
        values:
          - us-central1-b
          - us-central1-c
```

----------------------------------------

TITLE: Granting Required Permissions for n8n PostgresDB User (SQL)
DESCRIPTION: This SQL snippet provides the necessary commands to set up a new database and user for n8n in PostgresDB. It creates a dedicated database, defines a new user with a specified password, and grants all privileges on the newly created database to that user, ensuring n8n has the required access to manage its schemas.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/supported-databases-settings.md#_snippet_1

LANGUAGE: sql
CODE:
```
CREATE DATABASE n8n-db;
CREATE USER n8n-user WITH PASSWORD 'random-password';
GRANT ALL PRIVILEGES ON DATABASE n8n-db TO n8n-user;
```

----------------------------------------

TITLE: Configuring Query Parameters for MySQL Node - JavaScript
DESCRIPTION: This JavaScript snippet shows how to configure the 'Query Parameters' field in the n8n MySQL node. It provides the table name 'users' and an expression `{{ $json.email }}` to dynamically extract the email address from each incoming JSON item, enabling the parameterized SQL query to function correctly.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.mysql/index.md#_snippet_2

LANGUAGE: js
CODE:
```
// users is an example table name
users, {{ $json.email }}
```

----------------------------------------

TITLE: Defining Query Parameters for Postgres - JavaScript
DESCRIPTION: This JavaScript expression defines the values for the query parameters used in the SQL query. It dynamically pulls the table name ('users') and the email address from the current input item (`$json.email`), allowing the query to be executed for each incoming item.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.postgres/index.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
// users is an example table name
{{ [ 'users', $json.email ] }}
```

----------------------------------------

TITLE: Filtering NASA DONKI Solar Flare Data by Start Date (JavaScript)
DESCRIPTION: This JavaScript expression, used within n8n's expression editor, calculates a date exactly seven days prior to the current date. It's used to set the 'Start date' parameter for the NASA DONKI Solar Flare operation, limiting the report to recent events. It leverages n8n's built-in `$today` variable and Luxon-like date manipulation methods.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/try-it-out/tutorial-first-workflow.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
{{ $today.minus(7, 'days') }}
```

----------------------------------------

TITLE: Running Security Audit - n8n CLI
DESCRIPTION: This command initiates a security audit on the n8n instance to detect common security issues and vulnerabilities, helping to ensure a more secure deployment.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_24

LANGUAGE: sh
CODE:
```
n8n audit
```

----------------------------------------

TITLE: Running n8n in Docker on Linux with Host Gateway - Shell
DESCRIPTION: This shell command starts an n8n Docker container on a Linux host, configuring it to map host.docker.internal to host-gateway. This setup is crucial when n8n is containerized but MySQL runs directly on the host, enabling n8n to access the host's MySQL server using the host.docker.internal address.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.mysql/common-issues.md#_snippet_2

LANGUAGE: Shell
CODE:
```
docker run -it --rm --add-host host.docker.internal:host-gateway --name n8n -p 5678:5678 -v n8n_data:/home/<USER>/.n8n docker.n8n.io/n8nio/n8n
```

----------------------------------------

TITLE: Resetting User Management - n8n CLI
DESCRIPTION: This command resets n8n's user management to its pre-setup state, effectively removing all existing user accounts. It is particularly useful for recovering access if passwords are lost and SMTP is not configured for email-based resets.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_21

LANGUAGE: sh
CODE:
```
n8n user-management:reset
```

----------------------------------------

TITLE: Implementing Execute Method for FriendGrid Node in TypeScript
DESCRIPTION: This snippet defines the `execute` method for an n8n node, which processes input data, makes API calls to SendGrid for contact creation, and returns the results. It demonstrates handling multiple input items and mapping node UI parameters to API request bodies.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/programmatic-style-node.md#_snippet_8

LANGUAGE: TypeScript
CODE:
```
// Handle data coming from previous nodes
const items = this.getInputData();
let responseData;
const returnData = [];
const resource = this.getNodeParameter('resource', 0) as string;
const operation = this.getNodeParameter('operation', 0) as string;

// For each item, make an API call to create a contact
for (let i = 0; i < items.length; i++) {
	if (resource === 'contact') {
		if (operation === 'create') {
			// Get email input
			const email = this.getNodeParameter('email', i) as string;
			// Get additional fields input
			const additionalFields = this.getNodeParameter('additionalFields', i) as IDataObject;
			const data: IDataObject = {
				email,
			};

			Object.assign(data, additionalFields);

			// Make HTTP request according to https://sendgrid.com/docs/api-reference/
			const options: OptionsWithUri = {
				headers: {
					'Accept': 'application/json',
				},
				method: 'PUT',
				body: {
					contacts: [
						data,
					],
				},
				uri: `https://api.sendgrid.com/v3/marketing/contacts`,
				json: true,
			};
			responseData = await this.helpers.requestWithAuthentication.call(this, 'friendGridApi', options);
			returnData.push(responseData);
		}
	}
}
// Map data to n8n data structure
return [this.helpers.returnJsonArray(returnData)];
```

----------------------------------------

TITLE: Building and Linking Custom n8n Node Locally (Shell)
DESCRIPTION: These commands are executed within your custom node's directory. `npm run build` compiles your node's source code, and `npm link` creates a symlink, making your node package available for local installation into other projects, such as your n8n instance.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_snippets/integrations/creating-nodes/testing.md#_snippet_1

LANGUAGE: shell
CODE:
```
npm run build
npm link
```

----------------------------------------

TITLE: Configuring Built-in and External Modules in n8n Code Node (Bash)
DESCRIPTION: This snippet demonstrates how to configure environment variables to allow specific built-in and external modules within the n8n Code node. It shows examples for enabling all built-in modules, specific built-in modules (crypto, fs), and specific external npm modules (moment, lodash) by setting `NODE_FUNCTION_ALLOW_BUILTIN` and `NODE_FUNCTION_ALLOW_EXTERNAL`.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-examples/modules-in-code-node.md#_snippet_0

LANGUAGE: bash
CODE:
```
# Allows usage of all builtin modules
export NODE_FUNCTION_ALLOW_BUILTIN=*

# Allows usage of only crypto
export NODE_FUNCTION_ALLOW_BUILTIN=crypto

# Allows usage of only crypto and fs
export NODE_FUNCTION_ALLOW_BUILTIN=crypto,fs

# Allow usage of external npm modules.
export NODE_FUNCTION_ALLOW_EXTERNAL=moment,lodash
```

----------------------------------------

TITLE: Setting Webhook URL for Reverse Proxy (Bash)
DESCRIPTION: This snippet sets the `WEBHOOK_URL` environment variable, which is crucial when n8n operates behind a reverse proxy. It ensures n8n generates and displays the correct external webhook URLs, overriding its default internal URL construction based on `N8N_PROTOCOL`, `N8N_HOST`, and `N8N_PORT`.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-examples/webhook-url.md#_snippet_0

LANGUAGE: bash
CODE:
```
export WEBHOOK_URL=https://n8n.example.com/
```

----------------------------------------

TITLE: Configuring NODES_EXCLUDE Environment Variable (Configuration)
DESCRIPTION: Example showing how to set the `NODES_EXCLUDE` environment variable to prevent specific nodes from loading. The value is a JSON array of node names. This is useful for blocking potentially risky nodes in a multi-user environment.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/environment-variables/nodes.md#_snippet_0

LANGUAGE: Configuration
CODE:
```
NODES_EXCLUDE: "[\"n8n-nodes-base.executeCommand\", \"n8n-nodes-base.readWriteFile\"]"
```

----------------------------------------

TITLE: Configuring HTTP Request Node for HTML Extraction (n8n Workflow Configuration)
DESCRIPTION: This configuration sets up an HTTP Request node to perform a GET request to the n8n blog URL. It's the first step in web scraping, fetching the raw HTML content for subsequent processing by the HTML node. No authentication is required for this endpoint.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-2.md#_snippet_0

LANGUAGE: JSON
CODE:
```
{
  "nodeType": "n8n-nodes-base.httpRequest",
  "parameters": {
    "authentication": "none",
    "requestMethod": "GET",
    "url": "https://blog.n8n.io/"
  }
}
```

----------------------------------------

TITLE: Converting and Printing JsProxy Data (Python)
DESCRIPTION: This Python snippet demonstrates how to convert `JsProxy` objects, which represent n8n's internal data structures, into native Python dictionaries using `item.json.to_py()`. It iterates through data from a previous node and prints each converted item for detailed inspection and debugging.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/code-node/console-log.md#_snippet_3

LANGUAGE: python
CODE:
```
previousNodeData = _("<node-name>").all();
for item in previousNodeData:
	# item is of type <class 'pyodide.ffi.JsProxy'>
	# You need to convert it to a Dict
	itemDict = item.json.to_py()
	print(itemDict)
```

----------------------------------------

TITLE: Accessing Environment Variables in JavaScript
DESCRIPTION: This method contains the Variables available in the active n8n environment. It provides access to user-defined or system-defined variables.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/n8n-metadata.md#_snippet_14

LANGUAGE: JavaScript
CODE:
```
$vars
```

----------------------------------------

TITLE: Exposing Local Server with ngrok (Shell)
DESCRIPTION: This command uses ngrok to expose a local server running on port 5678 to the internet, generating a publicly accessible URL. This is useful for configuring OAuth credentials in a local development environment where `localhost` URLs are not accepted by services like Twist.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/twist.md#_snippet_0

LANGUAGE: sh
CODE:
```
ngrok http 5678
```

----------------------------------------

TITLE: Retrieve All Items from Node - JavaScript
DESCRIPTION: These methods retrieve data items from a specified upstream node. They allow for optional branchIndex and runIndex parameters to target specific outputs or workflow runs. When branchIndex is omitted, the method defaults to the output directly connecting the source node to the current node, making them suitable for use within the n8n Code node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/output-other-nodes.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
$("<node-name>").all(branchIndex?, runIndex?)
```

----------------------------------------

TITLE: Setting Single Custom Execution Data (Code Node)
DESCRIPTION: This snippet demonstrates how to set a single key-value pair as custom execution data for an n8n workflow using the Code node. This data can be used for filtering workflow executions later.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/workflows/executions/custom-executions-data.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
$execution.customData.set("key", "value");
```

LANGUAGE: Python
CODE:
```
_execution.customData.set("key", "value");
```

----------------------------------------

TITLE: Defining an Array of Objects in JavaScript
DESCRIPTION: This JavaScript snippet defines an array named 'turtles' containing four objects. Each object represents a turtle with 'name' and 'color' properties, demonstrating the structure n8n expects for collections of items.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-1.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
var turtles = [
	{
		name: 'Michelangelo',
		color: 'orange',
	},
	{
		name: 'Donatello',
		color: 'purple',
	},
	{
		name: 'Raphael',
		color: 'red',
	},
	{
		name: 'Leonardo',
		color: 'blue',
	}
];
```

----------------------------------------

TITLE: Getting Previous Node Output Index in Python
DESCRIPTION: This method provides the index of the output connector from which the current input was received, particularly useful when the previous node has multiple outputs (e.g., If or Switch nodes). When using a Merge node, it consistently refers to the first input connector.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/n8n-metadata.md#_snippet_28

LANGUAGE: Python
CODE:
```
_prevNode.outputIndex
```

----------------------------------------

TITLE: Setting Resource Requests and Limits for Kubernetes Pods (YAML)
DESCRIPTION: This YAML snippet specifies the memory resource requests and limits for application containers within Kubernetes deployments. It sets a minimum memory request of 250Mi and a maximum limit of 500Mi, allowing Kubernetes to manage CPU resources automatically. These values can be adjusted based on specific application needs.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/google-cloud.md#_snippet_4

LANGUAGE: yaml
CODE:
```
resources:
  requests:
    memory: "250Mi"
  limits:
    memory: "500Mi"
```

----------------------------------------

TITLE: Caddyfile Configuration for n8n Domain (Caddyfile)
DESCRIPTION: This Caddyfile snippet configures Caddy to serve a specific domain (e.g., `n8n.example.com`) and reverse proxy requests to the n8n service running on port 5678 within the Docker network. The `flush_interval -1` setting is used to optimize proxy behavior.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/digital-ocean.md#_snippet_11

LANGUAGE: Caddyfile
CODE:
```
n8n.<domain>.<suffix> {
    reverse_proxy n8n:5678 {
      flush_interval -1
    }
}
```

----------------------------------------

TITLE: Extracting First and Last Names with JMESPath Expression (JavaScript)
DESCRIPTION: This JMESPath expression uses multiselect list syntax to extract the 'first' and 'last' names from each person in the 'people' array, creating a new list of name pairs. It's designed for direct use within n8n expressions.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/jmespath.md#_snippet_8

LANGUAGE: js
CODE:
```
{{$jmespath($json.body.people, "[].[first, last]")}}
```

----------------------------------------

TITLE: Building a Custom n8n Docker Image
DESCRIPTION: This command builds a Docker image based on the provided Dockerfile, allowing for the inclusion of custom n8n nodes. It requires specifying the target n8n version as a build argument, which is then used within the Dockerfile to install the correct n8n release. The resulting image is tagged as 'customizedn8n' and can be used to run n8n with your private nodes.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/deploy/install-private-nodes.md#_snippet_1

LANGUAGE: bash
CODE:
```
docker build --build-arg N8N_VERSION=<n8n-version-number> --tag=customizedn8n .
```

----------------------------------------

TITLE: Querying Category ID by Name with JMESPath (JavaScript)
DESCRIPTION: This JMESPath expression queries a list of objects to find the `category_id` of the item where the `json.name` property is 'Lenovo'. It demonstrates filtering and projection using JMESPath within an n8n expression.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/jmespath.md#_snippet_12

LANGUAGE: js
CODE:
```
{{ $jmespath($("Code").all(), "[?json.name=='Lenovo'].json.category_id") }}
```

----------------------------------------

TITLE: Configuring n8n and Traefik with Docker Compose (YAML)
DESCRIPTION: This Docker Compose configuration defines two services: `traefik` for reverse proxying and SSL/TLS management, and `n8n` for the workflow automation platform. It sets up port mappings, persistent volumes for data and certificates, and environment variables for n8n, while Traefik is configured to use Docker as a provider and handle ACME challenges for SSL certificates.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/docker-compose.md#_snippet_8

LANGUAGE: yaml
CODE:
```
services:
  traefik:
    image: "traefik"
    restart: always
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.web.http.redirections.entryPoint.to=websecure"
      - "--entrypoints.web.http.redirections.entrypoint.scheme=https"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.mytlschallenge.acme.tlschallenge=true"
      - "--certificatesresolvers.mytlschallenge.acme.email=${SSL_EMAIL}"
      - "--certificatesresolvers.mytlschallenge.acme.storage=/letsencrypt/acme.json"
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - traefik_data:/letsencrypt
      - /var/run/docker.sock:/var/run/docker.sock:ro

  n8n:
    image: docker.n8n.io/n8nio/n8n
    restart: always
    ports:
      - "127.0.0.1:5678:5678"
    labels:
      - traefik.enable=true
      - traefik.http.routers.n8n.rule=Host(`${SUBDOMAIN}.${DOMAIN_NAME}`)
      - traefik.http.routers.n8n.tls=true
      - traefik.http.routers.n8n.entrypoints=web,websecure
      - traefik.http.routers.n8n.tls.certresolver=mytlschallenge
      - traefik.http.middlewares.n8n.headers.SSLRedirect=true
      - traefik.http.middlewares.n8n.headers.STSSeconds=315360000
      - traefik.http.middlewares.n8n.headers.browserXSSFilter=true
      - traefik.http.middlewares.n8n.headers.contentTypeNosniff=true
      - traefik.http.middlewares.n8n.headers.forceSTSHeader=true
      - traefik.http.middlewares.n8n.headers.SSLHost=${DOMAIN_NAME}
      - traefik.http.middlewares.n8n.headers.STSIncludeSubdomains=true
      - traefik.http.middlewares.n8n.headers.STSPreload=true
      - traefik.http.routers.n8n.middlewares=n8n@docker
    environment:
      - N8N_HOST=${SUBDOMAIN}.${DOMAIN_NAME}
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      - NODE_ENV=production
      - WEBHOOK_URL=https://${SUBDOMAIN}.${DOMAIN_NAME}/
      - GENERIC_TIMEZONE=${GENERIC_TIMEZONE}
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./local-files:/files

volumes:
  n8n_data:
  traefik_data:
```

----------------------------------------

TITLE: n8n Workflow for PDF to JSON Conversion (JSON)
DESCRIPTION: This n8n workflow configuration demonstrates how to fetch a PDF file from a URL using an HTTP Request node and then convert its binary content into a structured JSON format using the Extract From File node. It includes a manual trigger to initiate the workflow and defines the connections between the nodes.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-2.md#_snippet_6

LANGUAGE: JSON
CODE:
```
{
	"name": "Binary to JSON",
	"nodes": [
		{
		"parameters": {},
		"id": "78639a25-b69a-4b9c-84e0-69e045bed1a3",
		"name": "When clicking \"Execute Workflow\"",
		"type": "n8n-nodes-base.manualTrigger",
		"typeVersion": 1,
		"position": [
			480,
			520
		]
		},
		{
		"parameters": {
			"url": "https://media.kaspersky.com/pdf/Kaspersky_Lab_Whitepaper_Anti_blocker.pdf",
			"options": {}
		},
		"id": "a11310df-1287-4e9a-b993-baa6bd4265a6",
		"name": "HTTP Request",
		"type": "n8n-nodes-base.httpRequest",
		"typeVersion": 4.1,
		"position": [
			700,
			520
		]
		},
		{
		"parameters": {
			"operation": "pdf",
			"options": {}
		},
		"id": "88697b6b-fb02-4c3d-a715-750d60413e9f",
		"name": "Extract From File",
		"type": "n8n-nodes-base.extractFromFile",
		"typeVersion": 1,
		"position": [
			920,
			520
		]
		}
	],
	"pinData": {},
	"connections": {
		"When clicking \"Execute Workflow\"": {
		"main": [
			[
			{
				"node": "HTTP Request",
				"type": "main",
				"index": 0
			}
			]
		]
		},
		"HTTP Request": {
		"main": [
			[
			{
				"node": "Extract From File",
				"type": "main",
				"index": 0
			}
			]
		]
		}
	}
}
```

----------------------------------------

TITLE: Accessing Custom Variables in n8n Workflows - JavaScript
DESCRIPTION: This snippet demonstrates how to access custom variables within n8n workflows using the `$vars` object. Variables are read-only strings and their values are replaced during workflow execution. If a variable has no value, it's treated as `undefined`.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/variables.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
// Access a variable
$vars.<variable-name>
```

----------------------------------------

TITLE: Mounting n8n Persistent Volume Claim (YAML)
DESCRIPTION: This YAML snippet from the `n8n-deployment.yaml` manifest demonstrates how a Persistent Volume Claim (PVC) named `n8n-claim0` is mounted into the n8n deployment. This ensures that files uploaded via n8n and manual encryption keys are persisted across pod restarts, enhancing data durability.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/aws.md#_snippet_4

LANGUAGE: yaml
CODE:
```
…
volumes:
  - name: n8n-claim0
    persistentVolumeClaim:
      claimName: n8n-claim0
…
```

----------------------------------------

TITLE: Checking for Empty Variable with Ternary Operator in n8n
DESCRIPTION: This snippet demonstrates using the ternary operator within an n8n expression to check if a variable from a previous node (`$json["variable_name"]`) is present. If the variable has a value, it is returned; otherwise, the string 'not found' is returned as a fallback. This is useful for handling missing or null data gracefully.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/expressions/check-incoming-data.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
{{$json["variable_name"]? $json["variable_name"] :"not found"}}
```

----------------------------------------

TITLE: Defining n8n Form Fields using JSON
DESCRIPTION: This JSON array defines a form structure for n8n, specifying various field types like date, dropdown, email, file, number, password, text, and textarea. It demonstrates how to use keys such as `fieldLabel`, `fieldType`, `requiredField`, `placeholder`, `fieldOptions` for dropdowns, `multiselect`, `multipleFiles`, and `acceptFileTypes` to configure form inputs.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.form.md#_snippet_1

LANGUAGE: javascript
CODE:
```
// Use the "requiredField" key on any field to mark it as mandatory
// Use the "placeholder" key to specify placeholder data for all fields
//     except 'dropdown', 'date' and 'file'

[
	{
		"fieldLabel": "Date Field",
		"fieldType": "date",
		"formatDate": "mm/dd/yyyy", // how to format received date in n8n
		"requiredField": true
	},
	{
		"fieldLabel": "Dropdown Options",
		"fieldType": "dropdown",
		"fieldOptions": {
			"values": [
				{
					"option": "option 1"
				},
				{
					"option": "option 2"
				}
			]
		},
		"requiredField": true
	},
	{
		"fieldLabel": "Multiselect",
		"fieldType": "dropdown",
		"fieldOptions": {
			"values": [
				{
					"option": "option 1"
				},
				{
					"option": "option 2"
				}
			]
		},
		"multiselect": true // setting to true allows multi-select
	},
	{
		"fieldLabel": "Email",
		"fieldType": "email",
		"placeholder": "<EMAIL>"
	},
	{
		"fieldLabel": "File",
		"fieldType": "file",
		"multipleFiles": true, // setting to true allows multiple files selection
		"acceptFileTypes": ".jpg, .png" // allowed file types
	},
	{
		"fieldLabel": "Number",
		"fieldType": "number"
	},
	{
		"fieldLabel": "Password",
		"fieldType": "password"
	},
	{
		// "fieldType": "text" can be omitted since it's the default type
		"fieldLabel": "Text"
	},
	{
		"fieldLabel": "Textarea",
		"fieldType": "textarea"
	}
]
```

----------------------------------------

TITLE: Supported Scopes for Gmail (API Configuration)
DESCRIPTION: These are the specific OAuth2 scopes required for integrating Gmail with n8n. These scopes grant various levels of access, from managing labels to composing messages, and are essential for Gmail-related operations within n8n.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/google/oauth-generic.md#_snippet_1

LANGUAGE: API Configuration
CODE:
```
https://www.googleapis.com/auth/gmail.labels
```

LANGUAGE: API Configuration
CODE:
```
https://www.googleapis.com/auth/gmail.addons.current.action.compose
```

LANGUAGE: API Configuration
CODE:
```
https://www.googleapis.com/auth/gmail.addons.current.message.action
```

LANGUAGE: API Configuration
CODE:
```
https://mail.google.com/
```

LANGUAGE: API Configuration
CODE:
```
https://www.googleapis.com/auth/gmail.modify
```

LANGUAGE: API Configuration
CODE:
```
https://www.googleapis.com/auth/gmail.compose
```

----------------------------------------

TITLE: Scheduling Monthly on 1st at Midnight with n8n Schedule Trigger (Cron)
DESCRIPTION: This Cron expression schedules a workflow to run on the first day of every month at midnight. The `1` in the day-of-month field specifies the execution date within each month.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger/index.md#_snippet_5

LANGUAGE: Cron
CODE:
```
0 0 1 * *
```

----------------------------------------

TITLE: Configuring Custom Auth Header and Query String (JSON)
DESCRIPTION: Provides an example of combining both HTTP headers (using the headers property) and query string parameters (using the qs property) within the JSON configuration for the Custom Auth credential type. This allows for flexible authentication setups.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/httprequest.md#_snippet_3

LANGUAGE: JSON
CODE:
```
{
	"headers": {
		"api-version": "202404"
	},
	"qs": {
		"apikey": "my-api-key"
	}
}
```

----------------------------------------

TITLE: Generating RSA Key Pair for Wise SCA
DESCRIPTION: This shell script uses the 'openssl' command-line tool to generate an RSA key pair. It first creates a 2048-bit private key named 'private.pem' and then extracts the corresponding public key from it, saving it as 'public.pem'. This key pair is essential for authenticating with Wise API endpoints that require Strong Customer Authentication (SCA).
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/wise.md#_snippet_0

LANGUAGE: sh
CODE:
```
$ openssl genrsa -out private.pem 2048
$ openssl rsa -pubout -in private.pem -out public.pem
```

----------------------------------------

TITLE: Reverting n8n Database Migrations (Self-Hosted)
DESCRIPTION: This command is used for self-hosted n8n instances to revert database migrations. It is a crucial step when rolling back to a previous n8n version, especially if the newer version included database schema changes. Before executing this command, any RBAC projects created in the newer version should be deleted.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/release-notes.md#_snippet_0

LANGUAGE: Shell
CODE:
```
n8n db:revert
```

----------------------------------------

TITLE: Increasing V8 Old Space Memory for n8n
DESCRIPTION: To prevent 'JavaScript heap out of memory' errors in self-hosted n8n, this snippet shows how to allocate more memory to the V8 engine's 'old memory' section. This is crucial for workflows processing large or long-lived data. The `SIZE` placeholder should be replaced with the desired memory in megabytes (e.g., 4096 for 4GB).
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/memory-errors.md#_snippet_0

LANGUAGE: Shell
CODE:
```
NODE_OPTIONS="--max-old-space-size=SIZE" n8n start
```

----------------------------------------

TITLE: Creating Docker Volume for n8n Data (Shell)
DESCRIPTION: This command creates a Docker volume named 'n8n_data'. This volume is essential for persisting n8n's application data, ensuring that user workflows, credentials, and other critical information are retained across container restarts.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/digital-ocean.md#_snippet_5

LANGUAGE: shell
CODE:
```
sudo docker volume create n8n_data
```

----------------------------------------

TITLE: Loading Sensitive Data from Files in Docker Compose
DESCRIPTION: This YAML snippet illustrates how to configure n8n to load sensitive data, such as credentials or database settings, from separate files using the `_FILE` suffix for environment variables in Docker Compose. This method enhances security by avoiding direct exposure of sensitive values.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-methods.md#_snippet_7

LANGUAGE: yaml
CODE:
```
CREDENTIALS_OVERWRITE_DATA_FILE=/path/to/credentials_data
DB_TYPE_FILE=/path/to/db_type
DB_POSTGRESDB_DATABASE_FILE=/path/to/database_name
DB_POSTGRESDB_HOST_FILE=/path/to/database_host
DB_POSTGRESDB_PORT_FILE=/path/to/database_port
DB_POSTGRESDB_USER_FILE=/path/to/database_user
DB_POSTGRESDB_PASSWORD_FILE=/path/to/database_password
DB_POSTGRESDB_SCHEMA_FILE=/path/to/database_schema
DB_POSTGRESDB_SSL_CA_FILE=/path/to/ssl_ca
DB_POSTGRESDB_SSL_CERT_FILE=/path/to/ssl_cert
DB_POSTGRESDB_SSL_KEY_FILE=/path/to/ssl_key
DB_POSTGRESDB_SSL_REJECT_UNAUTHORIZED_FILE=/path/to/ssl_reject_unauth
```

----------------------------------------

TITLE: Setting Initial Form Values with Query Parameters (URL)
DESCRIPTION: This URL demonstrates how to pre-populate form fields (`email` and `name`) using URL query parameters. Special characters like '@' and spaces are percent-encoded to ensure proper parsing by the n8n Form Trigger node in production mode.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.formtrigger.md#_snippet_0

LANGUAGE: URL
CODE:
```
https://my-account.n8n.cloud/form/my-form?email=jane.doe%40example.com&name=Jane%20Doe
```

----------------------------------------

TITLE: Deduplication Value Expression in n8n
DESCRIPTION: This n8n expression specifies the JSON path to the value used for deduplication. The `name` property of the current JSON item will be used to check for uniqueness against previously processed items, ensuring that only items with a new 'name' value are kept.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.removeduplicates/templates-and-examples.md#_snippet_1

LANGUAGE: n8n Expression
CODE:
```
{{ $json.name }}
```

----------------------------------------

TITLE: Using smartJoin to Transform Array of Objects (JavaScript)
DESCRIPTION: This snippet demonstrates how to use the `smartJoin` method in n8n to convert an array of objects into a single object. It takes an array of objects, where each object has 'type' and 'name' properties, and transforms it into an object where 'type' values become keys and 'name' values become their corresponding values. The method requires two string arguments: the key to use for the new object's keys and the key to use for the new object's values.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_snippets/data-function-code/smartjoin.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
{{ [{"type":"fruit", "name":"apple"},{"type":"vegetable", "name":"carrot"} ].smartJoin("type","name") }}
```

----------------------------------------

TITLE: Checking String for Email Format in n8n (JavaScript)
DESCRIPTION: This example demonstrates how to use the `isEmail()` data transformation function on a string within an n8n expression. It checks if the given string conforms to an email format and returns a boolean value (true in this specific case), simplifying email validation.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/data-transformation-functions/index.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
{{ "<EMAIL>".isEmail() }}

// Returns true
```

----------------------------------------

TITLE: Getting Current Run Index of n8n Node (Expression)
DESCRIPTION: This expression retrieves the current processing index of an n8n node, specifically 'Loop Over Items'. It provides the zero-based index of the item currently being processed, which can be used for tracking progress or for specific item-based operations.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.splitinbatches.md#_snippet_2

LANGUAGE: n8n Expression Language
CODE:
```
{{$node["Loop Over Items"].context["currentRunIndex"];}}
```

----------------------------------------

TITLE: Accessing /healthz/readiness Endpoint - n8n
DESCRIPTION: This snippet demonstrates how to access the `/healthz/readiness` endpoint. It returns a 200 HTTP status code if the n8n instance's database is connected and migrated, indicating the instance is ready to accept traffic.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/logging-monitoring/monitoring.md#_snippet_1

LANGUAGE: shell
CODE:
```
<your-instance-url>/healthz/readiness
```

----------------------------------------

TITLE: Transforming PokéAPI Results with n8n Code Node (JavaScript)
DESCRIPTION: This JavaScript code snippet is designed for use within an n8n Code node. It retrieves all input items, accesses the `results` array from the first item's JSON payload (expected from the PokéAPI HTTP request), and maps each result item into a new n8n-compatible JSON object. This prepares the data for further processing within the n8n workflow.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-1.md#_snippet_8

LANGUAGE: JavaScript
CODE:
```
let items = $input.all();
return items[0].json.results.map(item => {
	return {
		json: item
	}
});
```

----------------------------------------

TITLE: Running Ollama in Docker with Port Publishing (Shell)
DESCRIPTION: This command runs the Ollama container in detached mode, mapping the host's port 11434 to the container's port 11434, allowing n8n to connect to a locally hosted Ollama instance when only Ollama is containerized. It also mounts a volume for Ollama data and names the container 'ollama'.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmollama/common-issues.md#_snippet_0

LANGUAGE: Shell
CODE:
```
docker run -d -v ollama:/root/.ollama -p 11434:11434 --name ollama ollama/ollama
```

----------------------------------------

TITLE: Returning Calculated Data from n8n Code Node (JavaScript)
DESCRIPTION: This JavaScript snippet demonstrates the required return format for an n8n Code node. It shows how to encapsulate calculated variables, 'totalBooked' and 'bookedSum', within a 'json' object, which is then wrapped in an array. This specific structure ensures that the output is correctly processed by subsequent n8n nodes.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-one/chapter-5/chapter-5.5.md#_snippet_2

LANGUAGE: javascript
CODE:
```
return [{ json: {totalBooked, bookedSum} }]
```

----------------------------------------

TITLE: Retrieving All Items from a Node in n8n Expressions
DESCRIPTION: This snippet demonstrates how to use the `all()` method to retrieve items from a specified node. It shows examples for getting all items from the current node, from a named node, and from a specific output branch and run index of a named node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/builtin/all.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
// Returns all the items of the given node and current run
let allItems = $("<node-name>").all();

// Returns all items the node "IF" outputs (index: 0 which is Output "true" of its most recent run)
let allItems = $("IF").all();

// Returns all items the node "IF" outputs (index: 0 which is Output "true" of the same run as current node)
let allItems = $("IF").all(0, $runIndex);

// Returns all items the node "IF" outputs (index: 1 which is Output "false" of run 0 which is the first run)
let allItems = $("IF").all(1, 0);
```

LANGUAGE: Python
CODE:
```
# Returns all the items of the given node and current run
allItems = _("<node-name>").all();

# Returns all items the node "IF" outputs (index: 0 which is Output "true" of its most recent run)
allItems = _("IF").all();

# Returns all items the node "IF" outputs (index: 0 which is Output "true" of the same run as current node)
allItems = _("IF").all(0, _runIndex);

# Returns all items the node "IF" outputs (index: 1 which is Output "false" of run 0 which is the first run)
allItems = _("IF").all(1, 0);
```

----------------------------------------

TITLE: Setting Environment Variables via npm Command Line
DESCRIPTION: This snippet shows how to set a single environment variable for n8n using the `export` command in a bash terminal when running n8n via npm. This method applies the variable for the current session.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-methods.md#_snippet_0

LANGUAGE: bash
CODE:
```
export <variable>=<value>
```

----------------------------------------

TITLE: Extracting First Names with JMESPath List Projection
DESCRIPTION: This snippet demonstrates how to use JMESPath list projections to extract a list of all 'first' names from the 'people' array within the input JSON. It shows implementation in both n8n expressions and Code nodes for JavaScript and Python, returning an array of strings.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/jmespath.md#_snippet_4

LANGUAGE: JavaScript
CODE:
```
{{$jmespath($json.body.people, "[*].first" )}}
// Returns ["James", "Jacob", "Jayden"]
```

LANGUAGE: JavaScript
CODE:
```
let firstNames = $jmespath($json.body.people, "[*].first" )
return {firstNames};
/* Returns:
[
	{
		"firstNames": [
			"James",
			"Jacob",
			"Jayden"
		]
	}
]
*/
```

LANGUAGE: Python
CODE:
```
firstNames = _jmespath(_json.body.people, "[*].first" )
return {"firstNames":firstNames}
"""
Returns:
[
 	{
		"firstNames": [
			"James",
			"Jacob",
			"Jayden"
		]
	}
]
"""
```

----------------------------------------

TITLE: n8n JSON Output Configuration with Expressions
DESCRIPTION: This JSON configuration is used in the 'JSON Output' field of an n8n node. It defines a new JSON structure including a static key, an array containing values from input fields using n8n expressions ({{ $json.id }}, {{ $json.name }}), and a nested object also referencing input fields.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.set.md#_snippet_3

LANGUAGE: JSON
CODE:
```
{
  "newKey": "new value",
  "array": [{{ $json.id }},"{{ $json.name }}"],
  "object": {
    "innerKey1": "new value",
    "innerKey2": "{{ $json.id }}",
    "innerKey3": "{{ $json.name }}"
 }
}
```

----------------------------------------

TITLE: Accessing Workflow Static Data in JavaScript
DESCRIPTION: This method provides access to static workflow data. Note that static data does not persist during workflow testing; it requires the workflow to be active and triggered by a trigger or webhook to be saved.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/n8n-metadata.md#_snippet_5

LANGUAGE: JavaScript
CODE:
```
$getWorkflowStaticData(type)
```

----------------------------------------

TITLE: Installing n8n globally with npm
DESCRIPTION: This command installs the n8n command-line interface (CLI) globally on your system using npm. It's a prerequisite for running a local n8n instance, which is essential for testing custom nodes during development.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/node-development-environment.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install n8n -g
```

----------------------------------------

TITLE: Creating a Customer Message using n8n Expressions
DESCRIPTION: This expression dynamically generates a personalized message for each customer by embedding their name and description. It relies on data previously processed and made available in the workflow's JSON context, specifically the 'customer_name' and 'customer_description' fields from an upstream node like 'Edit Fields1'. The output is a formatted string ready for use in a messaging node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/try-it-out/quickstart.md#_snippet_0

LANGUAGE: n8n Expression
CODE:
```
Hi {{ $json.customer_name }}. Your description is: {{ $json.customer_description }}
```

----------------------------------------

TITLE: Parameterized SELECT Query for Postgres - SQL
DESCRIPTION: This SQL query demonstrates how to select data from a table using parameterized values. The `$1:name` syntax is used for a table name parameter, and `$2` is used for a column value parameter, preventing SQL injection by sanitizing inputs.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.postgres/index.md#_snippet_1

LANGUAGE: SQL
CODE:
```
SELECT * FROM $1:name WHERE email = $2;
```

----------------------------------------

TITLE: Setting Environment Variables via Docker Compose
DESCRIPTION: This YAML snippet demonstrates how to define environment variables for an n8n service within a `docker-compose.yaml` file. It uses the `environment` key to set variables like `N8N_TEMPLATES_ENABLED`, ensuring they are applied when the service starts.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-methods.md#_snippet_6

LANGUAGE: yaml
CODE:
```
n8n:
    environment:
      - N8N_TEMPLATES_ENABLED=false
```

----------------------------------------

TITLE: Extracting First and Last Names in Code Node (JavaScript)
DESCRIPTION: This JavaScript code snippet demonstrates how to use the `$jmespath` function within an n8n Code node to extract 'first' and 'last' names from the 'people' array and store them in a new list. The result is returned as part of a new object.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/jmespath.md#_snippet_9

LANGUAGE: js
CODE:
```
let newList = $jmespath($json.body.people, "[].[first, last]");
return {newList};
```

----------------------------------------

TITLE: Passing Data to Embedded Chat Trigger Node - JavaScript
DESCRIPTION: This snippet demonstrates how to pass custom metadata from a website to an embedded n8n Chat Trigger node using the `createChat` function. The `metadata` field allows arbitrary data, such as a user ID, to be included with the chat session, which can then be processed by downstream n8n nodes. This is useful for associating external context with chat interactions.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-langchain.chattrigger/common-issues.md#_snippet_0

LANGUAGE: javascript
CODE:
```
createChat({
	webhookUrl: 'YOUR_PRODUCTION_WEBHOOK_URL',
	metadata: {
		'YOUR_KEY': 'YOUR_DATA'
	}
});
```

----------------------------------------

TITLE: Filtering Supabase Metadata: Age Example
DESCRIPTION: This example provides a concrete application of filtering Supabase metadata. It shows how to query for records where the 'age' property within the metadata is greater than or equal to 21, utilizing the `gte` comparison operator.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.supabase/common-issues.md#_snippet_1

LANGUAGE: Supabase Query Language
CODE:
```
metadata->>age=gte.21
```

----------------------------------------

TITLE: Configuring HTML Node for Content Extraction (n8n Workflow Configuration)
DESCRIPTION: This configuration for the HTML node extracts specific content from the HTML output of the preceding HTTP Request node. It uses a CSS selector to target the title of the first blog post and returns its HTML value, demonstrating web scraping capabilities.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-2.md#_snippet_1

LANGUAGE: JSON
CODE:
```
{
  "nodeType": "n8n-nodes-base.html",
  "parameters": {
    "operation": "extractHtmlContent",
    "sourceData": "json",
    "jsonProperty": "data",
    "extractionValues": [
      {
        "key": "title",
        "cssSelector": ".post .item-title  a",
        "returnValue": "html"
      }
    ]
  }
}
```

----------------------------------------

TITLE: Configuring Custom Auth Query Parameters (JSON)
DESCRIPTION: Illustrates how to specify multiple query string parameters (appid, apikey) using the qs property for the Custom Auth credential type. These key-value pairs are appended to the request URL as query parameters.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/httprequest.md#_snippet_2

LANGUAGE: JSON
CODE:
```
{
	"qs": { 
		"appid": "123456",
		"apikey": "my-api-key"
	}
}
```

----------------------------------------

TITLE: Referencing Content for Sentiment Analysis (n8n Expression)
DESCRIPTION: This expression is used in the 'Text to Analyze' field to specify that the input text for sentiment analysis should come from a 'content' field within the incoming JSON data. This is a common use case for analyzing text from sources like blog posts or articles.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.sentimentanalysis.md#_snippet_1

LANGUAGE: n8n Expression
CODE:
```
{{ $json.content }}
```

----------------------------------------

TITLE: Defining RSS Feed URLs in n8n Code Node (JavaScript)
DESCRIPTION: This JavaScript snippet is designed for an n8n Code node, returning an array of objects. Each object contains a 'url' property, which is then used by subsequent nodes (like the RSS Read node) to dynamically fetch data from multiple sources in a loop.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-3.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
let urls = [
				{
					json: {
					url: 'https://medium.com/feed/n8n-io'
					}
				},
				{
				json: {
					url: 'https://dev.to/feed/n8n'
					}
				}
			]
			return urls;
```

----------------------------------------

TITLE: Defining n8n Pod Resource Requests and Limits (YAML)
DESCRIPTION: This YAML snippet from the `n8n-deployment.yaml` manifest specifies the resource requirements for the n8n application containers within Kubernetes. It sets a minimum memory request of 250MiB and a maximum memory limit of 500MiB, allowing Kubernetes to manage CPU allocation automatically. These values can be adjusted based on specific performance needs.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/aws.md#_snippet_5

LANGUAGE: yaml
CODE:
```
…
resources:
  requests:
    memory: "250Mi"
  limits:
    memory: "500Mi"
…    
```

----------------------------------------

TITLE: Deleting All Kubernetes Resources (Shell)
DESCRIPTION: This shell command `kubectl delete -f .` is used to remove all Kubernetes resources defined by the manifest files in the current directory from the cluster. This command is useful for cleaning up the deployment and freeing up cluster resources.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/google-cloud.md#_snippet_7

LANGUAGE: shell
CODE:
```
kubectl delete -f .
```

----------------------------------------

TITLE: Enabling Metrics and Healthz Endpoints - n8n Shell Configuration
DESCRIPTION: This snippet provides the environment variables required to enable the `/metrics` and `/healthz` endpoints for self-hosted n8n instances, as they are disabled by default. Set `N8N_METRICS=true` for metrics and `QUEUE_HEALTH_CHECK_ACTIVE=true` for healthz.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/logging-monitoring/monitoring.md#_snippet_3

LANGUAGE: shell
CODE:
```
N8N_METRICS=true
# healthz
QUEUE_HEALTH_CHECK_ACTIVE=true
```

----------------------------------------

TITLE: Creating Docker Volume for n8n Data - Shell
DESCRIPTION: This command creates a Docker volume named `n8n_data` using `sudo` for elevated privileges. This volume is essential for persisting n8n's application data, ensuring that user data and configurations are retained across container restarts.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/hetzner.md#_snippet_4

LANGUAGE: shell
CODE:
```
sudo docker volume create n8n_data
```

----------------------------------------

TITLE: Applying Kubernetes Namespace Manifest (Shell)
DESCRIPTION: This shell command `kubectl apply -f namespace.yaml` is used to specifically apply the namespace manifest. It can be run if an error about a missing 'n8n' namespace occurs when applying all manifests, ensuring the namespace is created before other resources.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/google-cloud.md#_snippet_6

LANGUAGE: shell
CODE:
```
kubectl apply -f namespace.yaml
```

----------------------------------------

TITLE: Example Caddyfile Configuration for Custom Domain (Caddyfile)
DESCRIPTION: This Caddyfile example demonstrates how to configure Caddy for an alternative subdomain, such as `automate.example.com`, while still reverse proxying traffic to the `n8n` service on port 5678. It illustrates the flexibility of Caddy's domain mapping.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/digital-ocean.md#_snippet_12

LANGUAGE: Caddyfile
CODE:
```
automate.example.com {
    reverse_proxy n8n:5678 {
      flush_interval -1
    }
}
```

----------------------------------------

TITLE: Configuring n8n Environment Variables in .env
DESCRIPTION: This snippet provides an example .env file content for configuring n8n. It defines DOMAIN_NAME, SUBDOMAIN (determining the n8n URL), an optional GENERIC_TIMEZONE, and SSL_EMAIL for TLS/SSL certificate creation.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/docker-compose.md#_snippet_6

LANGUAGE: env
CODE:
```
# DOMAIN_NAME and SUBDOMAIN together determine where n8n will be reachable from
# The top level domain to serve from
DOMAIN_NAME=example.com

# The subdomain to serve from
SUBDOMAIN=n8n

# The above example serve n8n at: https://n8n.example.com

# Optional timezone to set which gets used by Cron and other scheduling nodes
# New York is the default value if not set
GENERIC_TIMEZONE=Europe/Berlin

# The email address to use for the TLS/SSL certificate creation
SSL_EMAIL=<EMAIL>
```

----------------------------------------

TITLE: Example n8n API Workflow List Response (JSON)
DESCRIPTION: This JSON snippet illustrates the typical structure of a paginated response from the n8n API. It contains a `data` array holding the requested workflow objects and a `nextCursor` string. The `nextCursor` value is crucial for requesting subsequent pages of results.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/api/pagination.md#_snippet_1

LANGUAGE: javascript
CODE:
```
{
  "data": [
    // The response contains an object for each workflow
    {
      // Workflow data
    }
  ],
  "nextCursor": "MTIzZTQ1NjctZTg5Yi0xMmQzLWE0NTYtNDI2NjE0MTc0MDA"
}
```

----------------------------------------

TITLE: Confirming MySQL Port Number - SQL
DESCRIPTION: This SQL query displays the port number currently used by the MySQL server. This information is crucial for correctly configuring the port in the n8n database credential.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/mysql.md#_snippet_2

LANGUAGE: SQL
CODE:
```
SHOW VARIABLES WHERE Variable_name = 'port';
```

----------------------------------------

TITLE: Retrieve Last Item from Node - JavaScript
DESCRIPTION: These methods retrieve data items from a specified upstream node. They allow for optional branchIndex and runIndex parameters to target specific outputs or workflow runs. When branchIndex is omitted, the method defaults to the output directly connecting the source node to the current node, making them suitable for use within the n8n Code node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/output-other-nodes.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
$("<node-name>").last(branchIndex?, runIndex?)
```

----------------------------------------

TITLE: Configuring n8n with PostgresDB Environment Variables (Bash)
DESCRIPTION: This Bash script demonstrates how to configure n8n to use PostgresDB by setting essential environment variables such as database type, host, port, user, password, and schema. It also includes optional SSL configuration for secure connections and initiates the n8n service.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/supported-databases-settings.md#_snippet_0

LANGUAGE: bash
CODE:
```
export DB_TYPE=postgresdb
export DB_POSTGRESDB_DATABASE=n8n
export DB_POSTGRESDB_HOST=postgresdb
export DB_POSTGRESDB_PORT=5432
export DB_POSTGRESDB_USER=n8n
export DB_POSTGRESDB_PASSWORD=n8n
export DB_POSTGRESDB_SCHEMA=n8n

# optional:
export DB_POSTGRESDB_SSL_CA=$(pwd)/ca.crt
export DB_POSTGRESDB_SSL_REJECT_UNAUTHORIZED=false

n8n start
```

----------------------------------------

TITLE: String Data Transformation Functions in n8n JavaScript
DESCRIPTION: This snippet lists new JavaScript functions available for transforming string data within n8n workflows. It includes functions for converting to date-time, parsing JSON, extracting URL paths, converting to boolean, and encoding/decoding base64 strings. `toDateTime()` replaces `toDate()` for improved functionality.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/release-notes.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
toDateTime() //replaces toDate(). toDate() is retained for backwards compatability.
parseJson()
extractUrlPath()
toBoolean()
base64Encode()
base64Decode()
```

----------------------------------------

TITLE: Example: Retrieving First Item's Binary Data Buffer (JavaScript)
DESCRIPTION: This example shows a practical application of `getBinaryDataBuffer()`, specifically retrieving the binary data buffer for the first input item (`itemIndex = 0`) and using the default binary property name 'data'. It illustrates how to get the raw binary content for further processing.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/code-node/get-binary-data-buffer.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
let binaryDataBufferItem = await this.helpers.getBinaryDataBuffer(0, 'data');
// Returns the data in the binary buffer for the first input item
```

----------------------------------------

TITLE: Excluding specific n8n nodes using NODES_EXCLUDE
DESCRIPTION: This configuration snippet demonstrates how to use the `NODES_EXCLUDE` environment variable to prevent n8n users from accessing certain nodes. It specifies an array of node names, such as 'n8n-nodes-base.executeCommand' and 'n8n-nodes-base.readWriteFile', which will then be blocked across the n8n instance.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/securing/blocking-nodes.md#_snippet_0

LANGUAGE: Configuration
CODE:
```
NODES_EXCLUDE: \
```

LANGUAGE: undefined
CODE:
```
undefined
```

LANGUAGE: undefined
CODE:
```
undefined
```

LANGUAGE: undefined
CODE:
```
undefined
```

----------------------------------------

TITLE: Defining Create Operation and Email Field for n8n SendGrid Node (TypeScript)
DESCRIPTION: This snippet defines the 'operation' object and an 'email' field for an n8n node. The operation allows users to 'Create' a contact, typically mapping to a POST request. The 'email' field is a required string input for the contact's primary email address, displayed only when the 'create' operation and 'contact' resource are selected.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/programmatic-style-node.md#_snippet_6

LANGUAGE: TypeScript
CODE:
```
{
	displayName: 'Operation',
	name: 'operation',
	type: 'options',
	displayOptions: {
		show: {
			resource: [
				'contact',
			],
		},
	},
	options: [
		{
			name: 'Create',
			value: 'create',
			description: 'Create a contact',
			action: 'Create a contact',
		},
	],
	default: 'create',
	noDataExpression: true,
},
{
	displayName: 'Email',
	name: 'email',
	type: 'string',
	required: true,
	displayOptions: {
		show: {
			operation: [
				'create',
			],
			resource: [
				'contact',
			],
		},
	},
	default:'',
	placeholder: '<EMAIL>',
	description:'Primary email for the contact',
}
```

----------------------------------------

TITLE: Paginating with Body Parameters (n8n Expression)
DESCRIPTION: This snippet illustrates how to handle pagination when the API requires the page number to be sent as a body parameter in a POST request. It uses the $pageCount variable, incremented by one, to dynamically update the page number in each subsequent request.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/http-node/pagination.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
{{ $pageCount + 1 }}
```

----------------------------------------

TITLE: Defining n8n Frontend Hooks in JavaScript
DESCRIPTION: This JavaScript snippet illustrates the required structure for an n8n frontend external hook file. It defines the 'window.n8nExternalHooks' object, which contains various hook points (e.g., 'nodeView.mount', 'nodeView.createNodeActiveChanged'), each capable of holding an array of functions to be executed when the corresponding hook is triggered. Each hook function receives 'store' and 'meta' arguments.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/embed/configuration.md#_snippet_6

LANGUAGE: javascript
CODE:
```
window.n8nExternalHooks = {
  nodeView: {
    mount: [
      function (store, meta) {
        // do something
      },
    ],
    createNodeActiveChanged: [
      function (store, meta) {
        // do something
      },
      function (store, meta) {
        // do something else
      },
    ],
    addNodeButton: [
      function (store, meta) {
        // do something
      },
    ],
  },
};
```

----------------------------------------

TITLE: Populating a Field with AI-Generated Name (Full Parameters) - JavaScript
DESCRIPTION: Demonstrates how to use the `$fromAI()` expression with all optional parameters to dynamically populate a field with a name. It specifies the field key, a descriptive label, the expected data type, and a default value.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/advanced-ai/examples/using-the-fromai-function.md#_snippet_1

LANGUAGE: javascript
CODE:
```
$fromAI("name", "The commenter's name", "string", "Jane Doe")
```

----------------------------------------

TITLE: Configuring Custom Auth Headers (JSON)
DESCRIPTION: Demonstrates how to define multiple custom headers (X-AUTH-USERNAME, X-AUTH-PASSWORD) within the JSON configuration for the Custom Auth credential type. The headers property is used to specify key-value pairs for HTTP request headers.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/httprequest.md#_snippet_0

LANGUAGE: JSON
CODE:
```
{
	"headers": {
		"X-AUTH-USERNAME": "username",
		"X-AUTH-PASSWORD": "password"
	}
}
```

----------------------------------------

TITLE: Populating Stock Count with AI-Generated Number - JavaScript
DESCRIPTION: Illustrates using `$fromAI()` to dynamically populate a numerical field, such as the number of items in stock. It specifies the field key, a label, the 'number' data type, and a default value.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/advanced-ai/examples/using-the-fromai-function.md#_snippet_3

LANGUAGE: javascript
CODE:
```
$fromAI("numItemsInStock", "Number of items in stock", "number", 5)
```

----------------------------------------

TITLE: Filtering Supabase Metadata: Generic Query Structure
DESCRIPTION: This snippet illustrates the generic syntax for constructing a query to filter Supabase metadata. It demonstrates how to use the Postgres `->>` JSON operator to access properties within the metadata and apply comparison operators, following the Supabase metadata query language format.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.supabase/common-issues.md#_snippet_0

LANGUAGE: Supabase Query Language
CODE:
```
metadata->>{your-property}={comparison-operator}.{comparison-value}
```

----------------------------------------

TITLE: Basic JMESPath Method Syntax - JavaScript
DESCRIPTION: Illustrates the basic syntax for using the `jmespath()` method in n8n's JavaScript expressions to query a JSON object. The method takes the JSON object and a JMESPath search string as arguments.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/jmespath.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
$jmespath(object, searchString)
```

----------------------------------------

TITLE: Printing String to Console (Python)
DESCRIPTION: This Python snippet illustrates how to print a simple string variable 'a' to the browser console using the `print()` function. It's a basic debugging method for verifying variable contents in a Python Code node within n8n.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/code-node/console-log.md#_snippet_1

LANGUAGE: python
CODE:
```
a = "apple"
print(a)
```

----------------------------------------

TITLE: Cron Expression for Daily Trigger with Seconds
DESCRIPTION: This Cron expression is used to trigger a workflow daily at 04:08:30. The values represent seconds (30), minutes (8), hours (4), and '*' for day of month, month, and day of week, indicating 'every' for those fields. This format includes the optional seconds field.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_snippets/integrations/builtin/poll-modes.md#_snippet_0

LANGUAGE: Cron
CODE:
```
30 8 4 * * *
```

----------------------------------------

TITLE: Accessing /metrics Endpoint - n8n
DESCRIPTION: This snippet illustrates how to access the `/metrics` endpoint, which provides detailed information about the current status of your n8n instance. Note that this endpoint is not available on n8n Cloud.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/logging-monitoring/monitoring.md#_snippet_2

LANGUAGE: shell
CODE:
```
<your-instance-url>/metrics
```

----------------------------------------

TITLE: Creating n8n Workflow via REST API (POST)
DESCRIPTION: This snippet demonstrates how to create a new workflow in n8n by sending a POST request to the `/rest/workflows/` endpoint. It requires a JSON request body containing the workflow definition.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/embed/managing-workflows.md#_snippet_4

LANGUAGE: HTTP
CODE:
```
POST https://<n8n-domain>/rest/workflows/
```

----------------------------------------

TITLE: Example Webhook Data Structure (JSON)
DESCRIPTION: This JSON snippet illustrates the typical structure of data received by an n8n webhook trigger. It includes headers, parameters, query, and a 'body' property containing example user information (name, age, city) that can be accessed in subsequent workflow steps.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/expressions.md#_snippet_0

LANGUAGE: JSON
CODE:
```
[
  {
    "headers": {
      "host": "n8n.instance.address",
      ...
    },
    "params": {},
    "query": {},
    "body": {
      "name": "Jim",
      "age": 30,
      "city": "New York"
    }
  }
]
```

----------------------------------------

TITLE: Calculating Relative Dates from Today in n8n (JavaScript)
DESCRIPTION: This snippet shows how to calculate a date a specific number of days before or after the current date using Luxon's `minus()` method. It leverages n8n's `$today` variable, which represents the current date at midnight UTC, to easily determine past or future dates.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/luxon.md#_snippet_6

LANGUAGE: JavaScript
CODE:
```
{{$today.minus({days: 7})}}
```

LANGUAGE: JavaScript
CODE:
```
let sevenDaysAgo = $today.minus({days: 7})
```

----------------------------------------

TITLE: Configuring Basic String UI Element in n8n (TypeScript)
DESCRIPTION: This snippet demonstrates the basic configuration for a string UI element in n8n. It defines properties like `displayName`, `name`, `type`, `required`, `default` value, and a `description`. The `displayOptions` specify when this element should be shown based on resource and operation names.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/ui-elements.md#_snippet_0

LANGUAGE: TypeScript
CODE:
```
{
	displayName: Name, // The value the user sees in the UI
	name: name, // The name used to reference the element UI within the code
	type: string,
	required: true, // Whether the field is required or not
	default: 'n8n',
	description: 'The name of the user',
	displayOptions: { // the resources and operations to display this element with
		show: {
			resource: [
				// comma-separated list of resource names
			],
			operation: [
				// comma-separated list of operation names
			]
		}
	},
}
```

----------------------------------------

TITLE: Scheduling Every 5 Minutes with n8n Schedule Trigger (Cron)
DESCRIPTION: This Cron expression schedules a workflow to run every 5 minutes. The `*/5` in the minute field indicates that the workflow will execute every fifth minute of the hour.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger/index.md#_snippet_1

LANGUAGE: Cron
CODE:
```
*/5 * * * *
```

----------------------------------------

TITLE: Setting N8N Executions Mode to Queue (Bash)
DESCRIPTION: This command configures the EXECUTIONS_MODE environment variable to queue for n8n instances. Setting this on both the main instance and worker nodes enables n8n to operate in queue mode, allowing for scalable, distributed workflow execution. It's recommended to use Postgres 13+ with this mode.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/queue-mode.md#_snippet_1

LANGUAGE: Bash
CODE:
```
export EXECUTIONS_MODE=queue
```

----------------------------------------

TITLE: Extracting Name from Linked Item in Previous Node Output (JavaScript)
DESCRIPTION: This expression demonstrates how to access a specific property, 'name', from a linked item originating from a previous node's output. It navigates through the linked item's JSON data to retrieve the desired value.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/data/data-mapping/data-mapping-expressions.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
{{$("<node-name>").item.json.name}}
```

----------------------------------------

TITLE: Logging String to Console (JavaScript)
DESCRIPTION: This JavaScript snippet demonstrates how to log a simple string variable 'a' to the browser console using `console.log()`. It's a fundamental debugging technique to inspect variable values within an n8n Code node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/code-node/console-log.md#_snippet_0

LANGUAGE: js
CODE:
```
let a = "apple";
console.log(a);
```

----------------------------------------

TITLE: Starting Local n8n Instance (Shell)
DESCRIPTION: This command initiates the n8n application locally. After starting, you can access the n8n interface in your web browser to verify that your custom nodes are loaded and functional within the nodes panel.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_snippets/integrations/creating-nodes/testing.md#_snippet_3

LANGUAGE: shell
CODE:
```
n8n start
```

----------------------------------------

TITLE: Defining Backend External Hooks in n8n (JavaScript)
DESCRIPTION: This JavaScript code defines the structure for an n8n backend hook file, exporting an object where keys represent hook categories (e.g., 'frontend', 'workflow') and values are objects containing specific hook names. Each hook name maps to an array of asynchronous functions that are executed when the corresponding event occurs. The example demonstrates modifying OAuth callback URLs and enforcing a limit on active workflows by querying the database.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/embed/configuration.md#_snippet_4

LANGUAGE: JavaScript
CODE:
```
module.exports = {
    "frontend": {
        "settings": [
            async function (settings) {
                settings.oauthCallbackUrls.oauth1 = 'https://n8n.example.com/oauth1/callback';
                settings.oauthCallbackUrls.oauth2 = 'https://n8n.example.com/oauth2/callback';
            }
        ]
    },
    "workflow": {
        "activate": [
            async function (workflowData) {
                const activeWorkflows = await this.dbCollections.Workflow.count({ active: true });

                if (activeWorkflows > 1) {
                    throw new Error(
                        'Active workflow limit reached.'
                    );
                }
            }
        ]
    }
}
```

----------------------------------------

TITLE: Configuring Caddy Reverse Proxy for n8n - Caddyfile
DESCRIPTION: This Caddyfile configuration block defines a reverse proxy for n8n. It maps a specified domain (e.g., `n8n.example.com`) to the n8n service running on port 5678 within the Docker network, ensuring proper routing of web traffic.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/hetzner.md#_snippet_10

LANGUAGE: Caddyfile
CODE:
```
n8n.<domain>.<suffix> {
    reverse_proxy n8n:5678 {
      flush_interval -1
    }
}
```

----------------------------------------

TITLE: Aggregating Multiple Items into a Single Item in n8n Code Node (JavaScript)
DESCRIPTION: This JavaScript snippet illustrates how to consolidate multiple incoming n8n items into a single output item. It collects the `json` payload of all input items into an array and assigns it to a new `data_object` property within a single output item.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-1.md#_snippet_6

LANGUAGE: JavaScript
CODE:
```
return [
	{
    	json: {
    		data_object: $input.all().map(item => item.json)
    	}
    }
  ];
```

----------------------------------------

TITLE: Defining Resource Mapper Type Options Interface (TypeScript)
DESCRIPTION: This snippet defines the TypeScript interface `ResourceMapperTypeOptions`, which specifies the structure and available properties for configuring the resource mapper component. It details properties such as `resourceMapperMethod` for schema fetching, `mode` for operation type (add, update, upsert), UI labels (`fieldWords`, `matchingFieldsLabels`), and flags for features like `addAllFields`, `multiKeyMatch`, and `supportAutoMap`. This interface ensures type safety and guides developers in correctly configuring the resource mapper.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/ui-elements.md#_snippet_15

LANGUAGE: TypeScript
CODE:
```
export interface ResourceMapperTypeOptions {
	// The name of the method where you fetch the schema
	// Refer to the Resource mapper method section for more detail
	resourceMapperMethod: string;
	// Choose the mode for your operation
	// Supported modes: add, update, upsert
	mode: 'add' | 'update' | 'upsert';
	// Specify labels for fields in the UI
	fieldWords?: { singular: string; plural: string };
	// Whether n8n should display a UI input for every field when node first added to workflow
	// Default is true
	addAllFields?: boolean;
	// Specify a message to show if no fields are fetched from the service
	// (the call is successful but the response is empty)
	noFieldsError?: string;
	// Whether to support multi-key column matching
	// multiKeyMatch is for update and upsert only
	// Default is false
	// If true, the node displays a multi-select dropdown for the matching column selector
	multiKeyMatch?: boolean;
	// Whether to support automatic mapping
	// If false, n8n hides the mapping mode selector field and sets mappingMode to defineBelow
	supportAutoMap?: boolean;
	// Custom labels for the matching columns selector
	matchingFieldsLabels?: {
		title?: string;
		description?: string;
		hint?: string;
	};
}
```

----------------------------------------

TITLE: Configuring n8n Execution Data Saving (Docker Compose)
DESCRIPTION: This snippet illustrates how to configure n8n to reduce saved execution data within a Docker Compose setup. Environment variables are defined under the `environment` key for the `n8n` service to control data saving preferences.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/execution-data.md#_snippet_2

LANGUAGE: yaml
CODE:
```
# Docker Compose
n8n:
    environment:
      - EXECUTIONS_DATA_SAVE_ON_ERROR=all
      - EXECUTIONS_DATA_SAVE_ON_SUCCESS=none
      - EXECUTIONS_DATA_SAVE_ON_PROGRESS=true
      - EXECUTIONS_DATA_SAVE_MANUAL_EXECUTIONS=false
```

----------------------------------------

TITLE: Formatting JSON Parameters in n8n HTTP Request Node
DESCRIPTION: This snippet demonstrates how to correctly format JSON parameters when using an expression in the n8n HTTP Request node. It shows that the entire JSON object must be wrapped in double curly brackets {{ }} to be properly interpreted as an expression, ensuring valid JSON structure within.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/common-issues.md#_snippet_0

LANGUAGE: JSON
CODE:
```
{{    {
    "myjson":
    {
        "name1": "value1",
        "name2": "value2",
        "array1":
            ["value1","value2"]
    }
    }
}}
```

----------------------------------------

TITLE: Configuring n8n Execution Data Saving (Docker)
DESCRIPTION: This snippet demonstrates how to configure n8n to reduce saved execution data when running via Docker. It sets environment variables within the `docker run` command to control saving data for errors, successful executions, node progress, and manual executions.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/execution-data.md#_snippet_1

LANGUAGE: sh
CODE:
```
# Docker
docker run -it --rm \
 --name n8n \
 -p 5678:5678 \
 -e EXECUTIONS_DATA_SAVE_ON_ERROR=all \
 -e EXECUTIONS_DATA_SAVE_ON_SUCCESS=none \
 -e EXECUTIONS_DATA_SAVE_ON_PROGRESS=true \
 -e EXECUTIONS_DATA_SAVE_MANUAL_EXECUTIONS=false \
 docker.n8n.io/n8nio/n8n
```

----------------------------------------

TITLE: Excluding Specific Nodes (Environment Variable)
DESCRIPTION: This snippet illustrates how to use the `NODES_EXCLUDE` environment variable to prevent specific nodes from being loaded in n8n. This is particularly useful for security, allowing administrators to block potentially risky nodes like `executeCommand` and `readWriteFile`.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/embed/configuration.md#_snippet_3

LANGUAGE: sh
CODE:
```
NODES_EXCLUDE: "[\"n8n-nodes-base.executeCommand\", \"n8n-nodes-base.readWriteFile\"]"
```

----------------------------------------

TITLE: Importing All Workflows from Directory - n8n CLI
DESCRIPTION: This command imports all workflow files (expected as JSON) from a specified directory into n8n. The `--separate` flag ensures each file is treated as a distinct workflow import.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_16

LANGUAGE: bash
CODE:
```
n8n import:workflow --separate --input=backups/latest/
```

----------------------------------------

TITLE: Scheduling Daily at 6 AM with n8n Schedule Trigger (Cron)
DESCRIPTION: This Cron expression schedules a workflow to run daily at 6:00 AM. The `0` for minutes and `6` for hours specify the exact time of execution each day.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger/index.md#_snippet_3

LANGUAGE: Cron
CODE:
```
0 6 * * *
```

----------------------------------------

TITLE: Checking Node Execution Status in n8n JavaScript
DESCRIPTION: This JavaScript expression checks if a specific n8n node has been executed. It's useful for conditional logic to prevent errors when referencing unexecuted nodes, ensuring data availability before attempting to use it.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/expressions/common-issues.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
$("<node-name>").isExecuted
```

----------------------------------------

TITLE: Disabling MFA for User - n8n CLI
DESCRIPTION: This command disables Multi-Factor Authentication (MFA) for a specific user identified by their email address. It allows a user who has lost their recovery codes to log back in and set up MFA again.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_22

LANGUAGE: sh
CODE:
```
n8n mfa:disable --email=<EMAIL>
```

----------------------------------------

TITLE: Creating Docker Volume for Caddy Data (Shell)
DESCRIPTION: This command creates a Docker volume named 'caddy_data'. This volume is used to persist Caddy's cache and configuration between container restarts, improving performance and ensuring data integrity for the reverse proxy.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/digital-ocean.md#_snippet_4

LANGUAGE: shell
CODE:
```
sudo docker volume create caddy_data
```

----------------------------------------

TITLE: Accessing /healthz Endpoint - n8n
DESCRIPTION: This snippet shows how to access the `/healthz` endpoint to check if your n8n instance is reachable. A 200 HTTP status code indicates reachability, but not database status. This endpoint is available for both self-hosted and n8n Cloud users.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/logging-monitoring/monitoring.md#_snippet_0

LANGUAGE: shell
CODE:
```
<your-instance-url>/healthz
```

----------------------------------------

TITLE: Example of Invalid Syntax in n8n Expression (Trailing Period)
DESCRIPTION: This JSON snippet illustrates an invalid expression syntax due to a trailing period. The `{{ $('If').item.json. }}` expression is incomplete and will cause a syntax error, highlighting the importance of correct expression formatting in n8n.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/expressions/common-issues.md#_snippet_2

LANGUAGE: JSX
CODE:
```
{
  "my_field_1": "value",
  "my_field_2": {{ $('If').item.json. }}
}
```

----------------------------------------

TITLE: Configuring XML Node for JSON to XML Conversion (n8n Workflow Configuration)
DESCRIPTION: This configuration for the XML node converts the JSON output from the preceding HTTP Request node into XML format. It specifies the 'data' property as the source for conversion, illustrating how to transform data between different web service formats.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-2.md#_snippet_3

LANGUAGE: JSON
CODE:
```
{
  "nodeType": "n8n-nodes-base.xml",
  "parameters": {
    "mode": "jsonToXml",
    "propertyName": "data"
  }
}
```

----------------------------------------

TITLE: Cron Expression for Daily Trigger without Seconds
DESCRIPTION: This Cron expression triggers a workflow daily at 04:08. The values represent minutes (8), hours (4), and '*' for day of month, month, and day of week. The seconds field is omitted, demonstrating a common Cron format where seconds are not specified.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_snippets/integrations/builtin/poll-modes.md#_snippet_1

LANGUAGE: Cron
CODE:
```
8 4 * * *
```

----------------------------------------

TITLE: Referencing Input Parameters in n8n JSON Expression
DESCRIPTION: This JSON object demonstrates how to reference input parameters using an n8n expression. The `{{ $input.params }}` syntax attempts to access the parameters of the incoming data, which will cause an error if the node is tested without connected input.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/expressions/common-issues.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
{
  "my_field_1": {{ $input.params }}
}
```

----------------------------------------

TITLE: Starting n8n and Caddy Services (Shell)
DESCRIPTION: This command starts the n8n and Caddy services defined in the `docker-compose.yml` file. The `-d` flag runs the containers in detached mode, allowing them to run in the background. This initiates the n8n application and its reverse proxy.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/digital-ocean.md#_snippet_13

LANGUAGE: shell
CODE:
```
sudo docker compose up -d
```

----------------------------------------

TITLE: Granting Docker Access to Current User
DESCRIPTION: This snippet provides commands to add the currently logged-in user to the docker group, allowing them to run Docker commands without sudo. It also registers the group membership with the current session.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/docker-compose.md#_snippet_2

LANGUAGE: bash
CODE:
```
sudo usermod -aG docker ${USER}
# Register the `docker` group memebership with current session without changing your primary group
exec sg docker newgrp
```

----------------------------------------

TITLE: Starting Docker Compose Services (Bash)
DESCRIPTION: This command initiates the Docker Compose services defined in the `compose.yaml` file. The `-d` flag runs the containers in detached mode, allowing them to run in the background without blocking the terminal.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/docker-compose.md#_snippet_9

LANGUAGE: bash
CODE:
```
sudo docker compose up -d
```

----------------------------------------

TITLE: Making HTTP Requests with n8n Helpers (TypeScript)
DESCRIPTION: This snippet demonstrates how to make HTTP requests within an n8n node's `execute` function using the built-in helpers. It shows two primary methods: `httpRequest` for requests without authentication and `httpRequestWithAuthentication` for requests requiring credentials, where `credentialTypeName` specifies the type of credential to use. Both methods accept an `options` object to configure the request.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/http-helpers.md#_snippet_0

LANGUAGE: TypeScript
CODE:
```
// If no auth needed
const response = await this.helpers.httpRequest(options);

// If auth needed
const response = await this.helpers.httpRequestWithAuthentication.call(
	this, 
	'credentialTypeName', // For example: pipedriveApi
	options,
);
```

----------------------------------------

TITLE: Granting Docker Access to a Specific User
DESCRIPTION: This snippet shows how to add a specified user (replace <USER_TO_RUN_DOCKER>) to the docker group, enabling them to execute Docker commands without sudo.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/docker-compose.md#_snippet_3

LANGUAGE: bash
CODE:
```
sudo usermod -aG docker <USER_TO_RUN_DOCKER>
```

----------------------------------------

TITLE: Adding Info-Level Log Messages in n8n Workflows - JavaScript
DESCRIPTION: This JavaScript snippet illustrates how to add custom log messages within n8n workflows using the `LoggerProxy` class. It shows importing `LoggerProxy` (aliased as `Logger`) and using its `info` method to log a message with additional metadata, such as workflow name and ID, for enhanced debugging and traceability. This requires the `LoggerProxy` to be initialized beforehand.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/logging-monitoring/logging.md#_snippet_1

LANGUAGE: javascript
CODE:
```
// You have to import the LoggerProxy. We rename it to Logger to make it easier

import {
	LoggerProxy as Logger
} from 'n8n-workflow';

// Info-level logging of a trigger function, with workflow name and workflow ID as additional metadata properties

Logger.info(`Polling trigger initiated for workflow "${workflow.name}"`, {workflowName: workflow.name, workflowId: workflow.id});
```

----------------------------------------

TITLE: Specifying Google OAuth Scopes for n8n Credential
DESCRIPTION: This snippet illustrates the format for providing multiple Google OAuth scopes when configuring an n8n credential. Scopes define the specific permissions your application requests from Google users. Multiple scopes must be provided as a single, space-separated string.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/google/oauth-generic.md#_snippet_0

LANGUAGE: Plain Text
CODE:
```
https://www.googleapis.com/auth/gmail.labels https://www.googleapis.com/auth/gmail.addons.current.action.compose
```

----------------------------------------

TITLE: Disabling 2FA for n8n Instance via Environment Variable (Shell)
DESCRIPTION: Self-hosted n8n users can disable two-factor authentication for all users on their instance by setting the N8N_MFA_ENABLED environment variable to 'false'. This setting is ignored if existing users already have 2FA enabled. Refer to n8n's configuration documentation for more details on applying environment variables.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/user-management/two-factor-auth.md#_snippet_0

LANGUAGE: Shell
CODE:
```
N8N_MFA_ENABLED=false
```

----------------------------------------

TITLE: Discord API Endpoint for Sending Messages
DESCRIPTION: This URL represents the Discord API endpoint for sending messages to a specific channel. It is used with an HTTP Request node to directly `POST` message and embed information, serving as a troubleshooting method when the standard Discord node has issues with embeds. Replace `<CHANNEL_ID>` with the actual Discord channel ID.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.discord/common-issues.md#_snippet_1

LANGUAGE: URL
CODE:
```
https://discord.com/api/v10/channels/<CHANNEL_ID>/messages
```

----------------------------------------

TITLE: Referencing and Adding Data to Items in n8n Code Node (JavaScript)
DESCRIPTION: This snippet shows how to access and modify data from previous nodes within an n8n Code node. It retrieves all incoming items, adds a new property `workEmail` to the first item's JSON, and assigns it the value of the existing `email.work` property.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-1.md#_snippet_4

LANGUAGE: JavaScript
CODE:
```
let items = $input.all();
items[0].json.workEmail = items[0].json.email['work'];
return items;
```

----------------------------------------

TITLE: Using Logical OR Operator for Default Values in n8n
DESCRIPTION: This snippet shows how to use the logical OR operator (||) in an n8n expression to provide a fallback. If the variable `$x` is a falsy value (e.g., `null`, `undefined`, `false`, `0`, `''`), the 'default value' string is used. Otherwise, the value of `$x` is returned. This is a common pattern for setting defaults.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/expressions/check-incoming-data.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
{{ $x || "default value" }}
```

----------------------------------------

TITLE: Access Linked Item - JavaScript
DESCRIPTION: This property provides access to the specific item in an upstream node that directly contributed to the creation of the current item. It's crucial for understanding data lineage but is explicitly not available for direct use within the n8n Code node. For tracing linked items in the Code node, itemMatching should be used instead.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/output-other-nodes.md#_snippet_3

LANGUAGE: JavaScript
CODE:
```
$("<node-name>").item
```

----------------------------------------

TITLE: Checking Data Type in Python Console
DESCRIPTION: This Python snippet uses `print(type(myData))` to output the data type of a variable `myData` to the console. This is essential for diagnosing `[object Object]` outputs and determining if a `JsProxy` conversion is necessary for n8n data.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/code-node/console-log.md#_snippet_2

LANGUAGE: python
CODE:
```
print(type(myData))
```

----------------------------------------

TITLE: Applying Kubernetes Namespace Manifest
DESCRIPTION: This command specifically applies the `namespace.yaml` manifest to create the 'n8n' namespace in the Kubernetes cluster. It addresses potential errors where the namespace is not found during initial manifest application.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/aws.md#_snippet_7

LANGUAGE: shell
CODE:
```
kubectl apply -f namespace.yaml
```

----------------------------------------

TITLE: Requesting Subsequent Pages of Workflows using n8n REST API (Bash)
DESCRIPTION: This example demonstrates how to fetch the next page of results using the `nextCursor` obtained from a previous API response. The `cursor` parameter is appended to the query string along with `active` and `limit` to continue pagination. An API key is not explicitly shown in this snippet, but would be required for authenticated access.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/api/pagination.md#_snippet_2

LANGUAGE: bash
CODE:
```
# For a self-hosted n8n instance
curl -X 'GET' \
  '<N8N_HOST>:<N8N_PORT>/<N8N_PATH>/api/v<version-number>/workflows?active=true&limit=150&cursor=MTIzZTQ1NjctZTg5Yi0xMmQzLWE0NTYtNDI2NjE0MTc0MDA' \
  -H 'accept: application/json'

# For n8n Cloud
curl -X 'GET' \
  '<your-cloud-instance>/api/v<version-number>/workflows?active=true&limit=150&cursor=MTIzZT41NjctZTg5Yi0xMmQzLWE0NTYtNDI2NjE0MTc0MDA' \
  -H 'accept: application/json'
```

----------------------------------------

TITLE: Defining Resource Mapper Field Interface in TypeScript
DESCRIPTION: This snippet defines the `ResourceMapperField` interface, which specifies the structure for mapping data schema fields within n8n nodes. It includes properties like ID, display name, data type, and flags for matching, required status, and UI visibility, guiding how n8n interacts with external service schemas.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/ui-elements.md#_snippet_16

LANGUAGE: typescript
CODE:
```
interface ResourceMapperField {
	id: string;
	displayName: string;
	defaultMatch: boolean;
	canBeUsedToMatch?: boolean;
	required: boolean;
	display: boolean;
	type?: FieldType;
	removed?: boolean;
	options?: INodePropertyOptions[];
}
```

----------------------------------------

TITLE: Scheduling Every 3rd Day at Midnight with n8n Schedule Trigger (Cron)
DESCRIPTION: This Cron expression schedules a workflow to run every third day at midnight. The `*/3` in the day-of-month field indicates execution on the 1st, 4th, 7th, etc., day of the month.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger/index.md#_snippet_6

LANGUAGE: Cron
CODE:
```
0 0 */3 * *
```

----------------------------------------

TITLE: Starting n8n Worker (Docker)
DESCRIPTION: This Docker command starts an n8n worker process in a container, named 'n8n-queue', mapping host port 5679 to container port 5678. This is used when deploying n8n workers via Docker, allowing for scalable workflow execution.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/queue-mode.md#_snippet_4

LANGUAGE: Shell
CODE:
```
docker run --name n8n-queue -p 5679:5678 docker.n8n.io/n8nio/n8n worker
```

----------------------------------------

TITLE: Setting Environment Variables via Docker Run Command
DESCRIPTION: This Docker command demonstrates how to set environment variables using the `-e` flag when running an n8n container. It includes port mapping and disabling templates as an example, ensuring the variable is applied to the container instance.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-methods.md#_snippet_1

LANGUAGE: docker
CODE:
```
docker run -it --rm \
 --name n8n \
 -p 5678:5678 \
 -e N8N_TEMPLATES_ENABLED="false" \
 docker.n8n.io/n8nio/n8n
```

----------------------------------------

TITLE: Implementing a Filter Component with Collection Options in n8n (TypeScript)
DESCRIPTION: This snippet showcases the 'filter' parameter type, designed for evaluating and filtering data, combined with a 'collection' type for user-configurable options. The 'filter' component's behavior (case sensitivity, type validation) is dynamically linked to the boolean parameters within the 'options' collection. This setup allows users to customize filtering logic.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/ui-elements.md#_snippet_10

LANGUAGE: typescript
CODE:
```
{
	displayName: 'Conditions',
	name: 'conditions',
	placeholder: 'Add Condition',
	type: 'filter',
	default: {},
	typeOptions: {
		filter: {
			// Use the user options (below) to determine filter behavior
			caseSensitive: '={{!$parameter.options.ignoreCase}}',
			typeValidation: '={{$parameter.options.looseTypeValidation ? "loose" : "strict"}}',
		},
	},
},
{
displayName: 'Options',
name: 'options',
type: 'collection',
placeholder: 'Add option',
default: {},
options: [
	{
		displayName: 'Ignore Case',
		description: 'Whether to ignore letter case when evaluating conditions',
		name: 'ignoreCase',
		type: 'boolean',
		default: true,
	},
	{
		displayName: 'Less Strict Type Validation',
		description: 'Whether to try casting value types based on the selected operator',
		name: 'looseTypeValidation',
		type: 'boolean',
		default: true,
	},
],
}
```

----------------------------------------

TITLE: Populating a Field with AI-Generated Name (Simplified) - JavaScript
DESCRIPTION: Shows a simplified usage of the `$fromAI()` expression when optional parameters are not required. It dynamically populates a field with a name using only the field key.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/advanced-ai/examples/using-the-fromai-function.md#_snippet_2

LANGUAGE: javascript
CODE:
```
$fromAI("name")
```

----------------------------------------

TITLE: Transforming Data to Comma-Separated Usernames in n8n Code Node - JavaScript
DESCRIPTION: This JavaScript snippet transforms an array of input items into a single comma-separated string of usernames. Each username is enclosed in double quotation marks. It maps over the input items to extract and format usernames, then joins them into a single string, returning it as a JSON object.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/ai-code.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
const items = $input.all();
const usernames = items.map((item) => `"${item.json.username}"`);
const result = usernames.join(", ");
return [{ json: { usernames: result } }];
```

----------------------------------------

TITLE: Enabling Metrics in n8n Configuration JSON
DESCRIPTION: This JSON snippet shows the correct format for enabling metrics within an n8n configuration file. It illustrates how nested properties are used to control specific endpoints like metrics.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-methods.md#_snippet_5

LANGUAGE: json
CODE:
```
{
	"endpoints": {
		"metrics": {
			"enable": true
		}
	}
}
```

----------------------------------------

TITLE: Exporting All n8n Workflows to a Single Directory File
DESCRIPTION: This command exports all n8n workflows into a single JSON file located within a specified directory. The `--output` flag defines the full path and filename for the exported data.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_8

LANGUAGE: bash
CODE:
```
n8n export:workflow --all --output=backups/latest/file.json
```

----------------------------------------

TITLE: Importing All Credentials from Directory - n8n CLI
DESCRIPTION: This command imports all credential files (expected as JSON) from a specified directory into n8n. The `--separate` flag ensures each file is treated as a distinct credential import.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_18

LANGUAGE: bash
CODE:
```
n8n import:credentials --separate --input=backups/latest/
```

----------------------------------------

TITLE: Installing a Community Node via npm (Shell)
DESCRIPTION: Install a community node package from the npm registry into the current directory (expected to be ~/.n8n/nodes). Replace n8n-nodes-nodeName with the actual package name.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/community-nodes/installation/manual-install.md#_snippet_2

LANGUAGE: shell
CODE:
```
npm i n8n-nodes-nodeName
```

----------------------------------------

TITLE: Trying n8n with npx (Bash)
DESCRIPTION: This command allows users to run n8n without a global installation, downloading necessary components on the fly. It's ideal for quick trials and accessing the n8n UI at http://localhost:5678.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/npm.md#_snippet_0

LANGUAGE: bash
CODE:
```
npx n8n
```

----------------------------------------

TITLE: Configuring `loadOptions` for Dynamic Data Loading in JavaScript
DESCRIPTION: This snippet demonstrates how to use the `loadOptions` object within the `methods` property to dynamically fetch user-specific settings from a service. It defines the `routing` for the API request (URL, method) and specifies `output` processing steps like extracting a `rootProperty`, setting key-value pairs using `setKeyValue`, and sorting the results alphabetically by `name` for GUI rendering.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/node-base-files/declarative-style-parameters.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
methods : {
	loadOptions: {
		routing: {
			request: {
				url: '/webhook/example-option-parameters',
				method: 'GET',
			},
			output: {
				postReceive: [
					{
						// When the returned data is nested under another property
						// Specify that property key
						type: 'rootProperty',
						properties: {
							property: 'responseData',
						},
					},
					{
						type: 'setKeyValue',
						properties: {
							name: '={{$responseItem.key}} ({{$responseItem.value}})',
							value: '={{$responseItem.value}}',
						},
					},
					{
						// If incoming data is an array of objects, sort alphabetically by key
						type: 'sort',
						properties: {
							key: 'name',
						},
					},
				],
			},
		},
	}
},
```

----------------------------------------

TITLE: HTTP Request Body for Sending Discord Messages with Embeds
DESCRIPTION: This JSON object serves as the request body for sending a Discord message via the HTTP Request node. It includes a `content` field for the main message text and an `embeds` array containing a rich embed object. This structure allows for comprehensive message customization, including author, URL, fields, and footer, directly through the Discord API.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.discord/common-issues.md#_snippet_2

LANGUAGE: json
CODE:
```
{
	"content": "Test",
	"embeds": [
		{
			"author": "My Name",
			"url": "https://discord.js.org",
			"fields": [
				{
					"name": "Regular field title",
					"value": "Some value here"
				}
			],
			"footer": {
				"text": "Some footer text here",
				"icon_url": "https://i.imgur.com/AfFp7pu.png"
			}
		}
	]
}
```

----------------------------------------

TITLE: Accessing Custom Values in n8n Sub-workflow Expressions
DESCRIPTION: This expression demonstrates how to access a custom value named 'myCustomValue' that has been passed to a sub-workflow. The value is retrieved from the output data of the 'Execute Sub-workflow Trigger' node, which serves as the entry point for the called workflow.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_snippets/integrations/builtin/cluster-nodes/langchain-sub-nodes/workflow-values.md#_snippet_0

LANGUAGE: n8n Expression
CODE:
```
{{ $('Execute Sub-workflow Trigger').item.json.myCustomValue }}
```

----------------------------------------

TITLE: Retrieving First Page of Workflows using n8n REST API (Shell)
DESCRIPTION: This snippet demonstrates how to fetch the initial page of active workflows from the n8n API. It uses `curl` to send a GET request, specifying `active=true` to filter for active workflows and `limit=150` to set the page size. An API key is required for authentication.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/api/pagination.md#_snippet_0

LANGUAGE: shell
CODE:
```
# For a self-hosted n8n instance
curl -X 'GET' \
  '<N8N_HOST>:<N8N_PORT>/<N8N_PATH>/api/v<version-number>/workflows?active=true&limit=150' \
  -H 'accept: application/json' \
  -H 'X-N8N-API-KEY: <your-api-key>'

# For n8n Cloud
curl -X 'GET' \
  '<your-cloud-instance>/api/v<version-number>/workflows?active=true&limit=150' \
  -H 'accept: application/json' \
  -H 'X-N8N-API-KEY: <your-api-key>'
```

----------------------------------------

TITLE: Defining an Assignment Collection for Drag-and-Drop in n8n (TypeScript)
DESCRIPTION: This snippet illustrates the 'assignmentCollection' parameter type, which facilitates drag-and-drop functionality for users to pre-fill name and value parameters. It's useful for scenarios like setting fields dynamically. The parameter defaults to an empty object.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/ui-elements.md#_snippet_11

LANGUAGE: typescript
CODE:
```
{
	displayName: 'Fields to Set',
	name: 'assignments',
	type: 'assignmentCollection',
	default: {},
}
```

----------------------------------------

TITLE: Configuring Base URL for n8n Front End (Bash)
DESCRIPTION: This snippet sets the `VUE_APP_URL_BASE_API` environment variable, which defines the base URL for n8n's front end to connect to its back end's REST API. This is crucial when hosting the front end and back end separately and requires a manual build of the `n8n-editor-ui` package.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-examples/base-url.md#_snippet_0

LANGUAGE: bash
CODE:
```
export VUE_APP_URL_BASE_API=https://n8n.example.com/
```

----------------------------------------

TITLE: Setting Permissions for Custom Certificates in n8n Docker Container
DESCRIPTION: This command changes the ownership of the mounted custom certificate directory inside the running n8n container to the n8n user (UID 1000). This ensures that n8n has the necessary read permissions for the certificates, preventing access issues.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-examples/custom-certificate-authority.md#_snippet_2

LANGUAGE: bash
CODE:
```
docker exec --user 0 n8n chown -R 1000:1000 /opt/custom-certificates
```

----------------------------------------

TITLE: Scheduling Weekly at Noon on Monday with n8n Schedule Trigger (Cron)
DESCRIPTION: This Cron expression schedules a workflow to run every Monday at 12:00 PM (noon). The `1` in the day-of-week field represents Monday, ensuring weekly execution on that specific day.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger/index.md#_snippet_4

LANGUAGE: Cron
CODE:
```
0 12 * * 1
```

----------------------------------------

TITLE: Implementing Single-Select Options in n8n Node Parameters (TypeScript)
DESCRIPTION: This snippet shows how to configure an 'options' parameter type for an n8n node, allowing users to select a single value from a predefined list. Each option has a display name and a corresponding value. A default option is also specified. The displayOptions property manages its visibility.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/ui-elements.md#_snippet_8

LANGUAGE: typescript
CODE:
```
{
	displayName: 'Resource',
	name: 'resource',
	type: 'options',
	options: [
		{
			name: 'Image',
			value: 'image',
		},
		{
			name: 'Template',
			value: 'template',
		},
	],
	default: 'image', // The initially selected option
	description: 'Resource to consume',
	displayOptions: { // the resources and operations to display this element with
		show: {
			resource: [
				// comma-separated list of resource names
			],
			operation: [
				// comma-separated list of operation names
			]
		}
	},
}
```

----------------------------------------

TITLE: Configuring Worker Concurrency
DESCRIPTION: This command starts an n8n worker process and sets its concurrency level, defining the maximum number of jobs it can run in parallel. Adjusting concurrency is important for optimizing resource usage and preventing database connection pool exhaustion.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/queue-mode.md#_snippet_9

LANGUAGE: bash
CODE:
```
n8n worker --concurrency=5
```

----------------------------------------

TITLE: Granting Scoped Access to AWS Secrets Manager for n8n (IAM Policy)
DESCRIPTION: This IAM policy provides n8n with restricted access to AWS Secrets Manager, allowing it to list and batch retrieve secrets globally, but only retrieve specific secret values (using `secretsmanager:GetSecretValue` and `secretsmanager:DescribeSecret`) for resources matching a specified ARN pattern (e.g., secrets starting with `n8n` in a particular region and account). This ensures a more granular control over secret access.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/external-secrets.md#_snippet_1

LANGUAGE: JSON
CODE:
```
{
	"Version": "2012-10-17",
	"Statement": [
		{
			"Sid": "ListingSecrets",
			"Effect": "Allow",
			"Action": [
				"secretsmanager:ListSecrets",
				"secretsmanager:BatchGetSecretValue"
			],
			"Resource": "*"
		},
		{
			"Sid": "RetrievingSecrets",
			"Effect": "Allow",
			"Action": [
				"secretsmanager:GetSecretValue",
				"secretsmanager:DescribeSecret"
			],
			"Resource": [
				"arn:aws:secretsmanager:us-west-2:************:secret:n8n*"
			]
		}
	]
}
```

----------------------------------------

TITLE: Parsing Non-Standard Date Strings with Luxon in n8n (JavaScript)
DESCRIPTION: This snippet demonstrates how to parse a date string that does not conform to a standard format using Luxon's `fromFormat()` function. It requires providing the date string and a set of tokens describing its format to convert it into a Luxon DateTime object, useful for custom date inputs.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/luxon.md#_snippet_5

LANGUAGE: JavaScript
CODE:
```
{{DateTime.fromFormat("23-06-2019", "dd-MM-yyyy")}}
```

LANGUAGE: JavaScript
CODE:
```
let newFormat = DateTime.fromFormat("23-06-2019", "dd-MM-yyyy")
```

----------------------------------------

TITLE: Configuring a Boolean Toggle in n8n Node Parameters (TypeScript)
DESCRIPTION: This snippet demonstrates how to define a boolean parameter type in an n8n node. It creates a toggle switch with a default value, allowing users to select true or false. The displayOptions property controls when this parameter is visible based on selected resources and operations.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/ui-elements.md#_snippet_6

LANGUAGE: typescript
CODE:
```
{
	displayName: 'Wait for Image',
	name: 'waitForImage',
	type: 'boolean',
	default: true, // Initial state of the toggle
	description: 'Whether to wait for the image or not',
	displayOptions: { // the resources and operations to display this element with
		show: {
			resource: [
				// comma-separated list of resource names
			],
			operation: [
				// comma-separated list of operation names
			]
		}
	},
}
```

----------------------------------------

TITLE: Converting ISO Date String to Luxon in n8n Code Node (JavaScript)
DESCRIPTION: This snippet shows how to convert an ISO 8601 formatted date string into a Luxon DateTime object within a JavaScript Code node. The `DateTime.fromISO()` function is utilized for this purpose, providing a reliable way to parse standard technical date formats.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/luxon.md#_snippet_4

LANGUAGE: JavaScript
CODE:
```
let luxonDateTime = DateTime.fromISO('2019-06-23T00:00:00.00')
```

----------------------------------------

TITLE: Running n8n CLI Commands with Docker
DESCRIPTION: This command demonstrates the general syntax for executing n8n CLI commands when n8n is running within a Docker container. It specifies running the command as the 'node' user and requires the n8n container's name and the specific n8n CLI command to be executed.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_0

LANGUAGE: sh
CODE:
```
docker exec -u node -it <n8n-container-name> <n8n-cli-command>
```

----------------------------------------

TITLE: Building a Custom n8n Docker Image with cURL
DESCRIPTION: This command builds a new Docker image named `n8n-curl` from the `Dockerfile` located in the current directory. This custom image will include the `curl` utility, allowing cURL commands to be executed within the n8n container.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.executecommand/index.md#_snippet_3

LANGUAGE: shell
CODE:
```
docker build -t n8n-curl
```

----------------------------------------

TITLE: Retrieving MySQL Connection Timeout - SQL
DESCRIPTION: This SQL query fetches the 'connect_timeout' variable from the MySQL server, which indicates the server's default connection timeout in seconds. This value can be used to inform the 'Connect Timeout' setting in n8n, after multiplying by 100 to convert to milliseconds.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/mysql.md#_snippet_3

LANGUAGE: SQL
CODE:
```
SHOW VARIABLES WHERE Variable_name = 'connect_timeout';
```

----------------------------------------

TITLE: Updating n8n Docker Image
DESCRIPTION: These commands allow pulling the latest stable or a specific version of the n8n Docker image from the registry. `docker pull docker.n8n.io/n8nio/n8n` fetches the most recent stable release, while `docker pull docker.n8n.io/n8nio/n8n:1.81.0` fetches a specified version.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/docker.md#_snippet_3

LANGUAGE: sh
CODE:
```
# Pull latest (stable) version
docker pull docker.n8n.io/n8nio/n8n

# Pull specific version
docker pull docker.n8n.io/n8nio/n8n:1.81.0
```

----------------------------------------

TITLE: Setting Global Workflow Timeout in Bash
DESCRIPTION: This snippet sets the global workflow execution timeout using the `EXECUTIONS_TIMEOUT` environment variable. The value is in seconds, so `3600` represents one hour. A value of `-1` indicates no timeout. This variable affects all workflows running in n8n.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-examples/execution-timeout.md#_snippet_0

LANGUAGE: bash
CODE:
```
export EXECUTIONS_TIMEOUT=3600
```

----------------------------------------

TITLE: Enabling n8n Execution Data Pruning (npm)
DESCRIPTION: This snippet shows how to activate and configure automatic data pruning for n8n when running with npm. It sets environment variables to enable pruning, define the maximum age of executions, and specify the maximum number of executions to store.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/execution-data.md#_snippet_3

LANGUAGE: sh
CODE:
```
# npm
# Activate automatic data pruning
export EXECUTIONS_DATA_PRUNE=true

# Number of hours after execution that n8n deletes data
export EXECUTIONS_DATA_MAX_AGE=168

# Number of executions to store
export EXECUTIONS_DATA_PRUNE_MAX_COUNT=50000
```

----------------------------------------

TITLE: Converting ISO Date String to Luxon in n8n Expressions (JavaScript)
DESCRIPTION: This example demonstrates how to convert an ISO 8601 formatted date string into a Luxon DateTime object directly within n8n expressions. The `DateTime.fromISO()` function is used for this conversion, which is suitable for parsing standard technical date formats.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/luxon.md#_snippet_3

LANGUAGE: JavaScript
CODE:
```
{{DateTime.fromISO('2019-06-23T00:00:00.00')}}
```

----------------------------------------

TITLE: Applying All Kubernetes Manifests (Shell)
DESCRIPTION: This command applies all Kubernetes manifest files (e.g., .yaml) found in the current directory to the cluster. It's used to create or update all defined resources, including deployments, services, and secrets, in one go.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/azure.md#_snippet_4

LANGUAGE: shell
CODE:
```
kubectl apply -f .
```

----------------------------------------

TITLE: Starting Redis in Docker
DESCRIPTION: This command starts a Redis instance within a Docker container, naming it 'some-redis' and mapping port 6379 from the container to the host. It runs in detached mode, providing a Redis server for n8n's queue management.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/queue-mode.md#_snippet_2

LANGUAGE: Shell
CODE:
```
docker run --name some-redis -p 6379:6379  -d redis
```

----------------------------------------

TITLE: Starting n8n and Caddy Services - Shell
DESCRIPTION: This command starts the n8n and Caddy services defined in the `docker-compose.yml` file in detached mode (`-d`). This allows the services to run in the background without tying up the terminal.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/hetzner.md#_snippet_11

LANGUAGE: shell
CODE:
```
docker compose up -d
```

----------------------------------------

TITLE: Calculating Duration Between Two Dates in n8n (JavaScript)
DESCRIPTION: This snippet explains how to determine the duration between two Luxon DateTime objects using the `diff()` method. It calculates the number of months between two specified ISO dates and returns the result as an object, providing a clear measure of the time elapsed.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/luxon.md#_snippet_9

LANGUAGE: JavaScript
CODE:
```
{{DateTime.fromISO('2019-06-23').diff(DateTime.fromISO('2019-05-23'), 'months').toObject()}}
```

LANGUAGE: JavaScript
CODE:
```
let monthsBetweenDates = DateTime.fromISO('2019-06-23').diff(DateTime.fromISO('2019-05-23'), 'months').toObject()
```

----------------------------------------

TITLE: Stopping n8n and Caddy Services (Shell)
DESCRIPTION: This command gracefully stops the n8n and Caddy services that were started using Docker Compose. It ensures that the containers are shut down properly, releasing resources and preventing data corruption.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/digital-ocean.md#_snippet_14

LANGUAGE: shell
CODE:
```
sudo docker compose stop
```

----------------------------------------

TITLE: Implementing `loadOptions` to Fetch Gmail Labels in JavaScript
DESCRIPTION: This snippet demonstrates how to implement the `loadOptions` method within a programmatic-style n8n node. It shows how to query the Gmail API to retrieve a user's email labels, format them as `INodePropertyOptions`, and return them for display in the n8n GUI. This allows users to select dynamic options based on their service data.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/node-base-files/programmatic-style-parameters.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
	methods = {
		loadOptions: {
			// Get all the labels and display them
			async getLabels(
				this: ILoadOptionsFunctions,
			): Promise<INodePropertyOptions[]> {
				const returnData: INodePropertyOptions[] = [];
				const labels = await googleApiRequestAllItems.call(
					this,
					'labels',
					'GET',
					'/gmail/v1/users/me/labels',
				);
				for (const label of labels) {
					const labelName = label.name;
					const labelId = label.id;
					returnData.push({
						name: labelName,
						value: labelId,
					});
				}
				return returnData;
			},
		},
	};
```

----------------------------------------

TITLE: Defining Generic API Credentials in n8n (TypeScript)
DESCRIPTION: This snippet outlines the basic structure for defining API credentials in n8n. It imports necessary interfaces, defines a class 'ExampleNode' implementing 'ICredentialType', and sets up properties like 'name', 'displayName', 'documentationUrl', 'properties' (for UI input), 'authenticate' (for request injection), and 'test' (for credential validation). It demonstrates how to configure an API key for query string authentication.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/credentials-files.md#_snippet_0

LANGUAGE: typescript
CODE:
```
import {
	IAuthenticateGeneric,
	ICredentialTestRequest,
	ICredentialType,
	INodeProperties,
} from 'n8n-workflow';

export class ExampleNode implements ICredentialType {
	name = 'exampleNodeApi';
	displayName = 'Example Node API';
	documentationUrl = '';
	properties: INodeProperties[] = [
		{
			displayName: 'API Key',
			name: 'apiKey',
			type: 'string',
			default: '',
		},
	];
	authenticate: IAuthenticateGeneric = {
		type: 'generic',
		properties: {
    		// Can be body, header, qs or auth
			qs: {
        		// Use the value from `apiKey` above
				'api_key': '={{$credentials.apiKey}}'
			}

		},
	};
	test: ICredentialTestRequest = {
		request: {
			baseURL: '={{$credentials?.domain}}',
			url: '/bearer',
		},
	};
}
```

----------------------------------------

TITLE: Accessing n8n Credential Schema Endpoint (Shell)
DESCRIPTION: This shell snippet illustrates the URL structure for retrieving the schema of a specific credential type via the n8n API. It uses environment variables for the host and path, and requires a '{credentialTypeName}' to fetch the detailed documentation for that credential.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/api/using-api-playground.md#_snippet_1

LANGUAGE: shell
CODE:
```
N8N_HOST:N8N_PORT/N8N_PATH/api/v<api-version-number>/credentials/schema/{credentialTypeName}
```

----------------------------------------

TITLE: Enable S3 Authentication Autodetection in n8n
DESCRIPTION: Set this environment variable to 'true' to enable n8n to automatically detect S3 credentials using the default AWS credential provider chain.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/external-storage.md#_snippet_3

LANGUAGE: sh
CODE:
```
export N8N_EXTERNAL_STORAGE_S3_AUTH_AUTO_DETECT=true
```

----------------------------------------

TITLE: Running a Security Audit using CLI
DESCRIPTION: This command initiates a security audit on your n8n instance directly from the command line interface. It's the simplest way to start an audit and will generate various risk reports.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/securing/security-audit.md#_snippet_0

LANGUAGE: Shell
CODE:
```
n8n audit
```

----------------------------------------

TITLE: Scheduling Quarterly on 1st at Midnight with n8n Schedule Trigger (Cron)
DESCRIPTION: This Cron expression schedules a workflow to run quarterly on the 1st of January, April, July, and October at midnight. The `1,4,7,10` in the month field specifies these specific months for execution.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger/index.md#_snippet_9

LANGUAGE: Cron
CODE:
```
0 0 1 1,4,7,10 *
```

----------------------------------------

TITLE: Dockerfile for n8n with Custom Node Support
DESCRIPTION: This Dockerfile is a modified version of the official n8n Dockerfile, designed to build a custom n8n image. It includes steps for installing n8n, its dependencies, and preparing the environment for custom nodes by setting up the working directory and entrypoint. This is a prerequisite for running n8n with private nodes in a Docker container.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/deploy/install-private-nodes.md#_snippet_0

LANGUAGE: Dockerfile
CODE:
```
FROM node:16-alpine

ARG N8N_VERSION

RUN if [ -z "$N8N_VERSION" ] ; then echo "The N8N_VERSION argument is missing!" ; exit 1; fi

# Update everything and install needed dependencies
RUN apk add --update graphicsmagick tzdata git tini su-exec

# Set a custom user to not have n8n run as root
USER root

# Install n8n and the packages it needs to build it correctly.
RUN apk --update add --virtual build-dependencies python3 build-base ca-certificates && \
	npm config set python "$(which python3)" && \
	npm_config_user=root npm install -g full-icu n8n@${N8N_VERSION} && \
	apk del build-dependencies \
	&& rm -rf /root /tmp/* /var/cache/apk/* && mkdir /root;


# Install fonts
RUN apk --no-cache add --virtual fonts msttcorefonts-installer fontconfig && \
	update-ms-fonts && \
	fc-cache -f && \
	apk del fonts && \
	find  /usr/share/fonts/truetype/msttcorefonts/ -type l -exec unlink {} \; \
	&& rm -rf /root /tmp/* /var/cache/apk/* && mkdir /root

ENV NODE_ICU_DATA /usr/local/lib/node_modules/full-icu

WORKDIR /data

COPY docker-entrypoint.sh /docker-entrypoint.sh
ENTRYPOINT ["tini", "--", "/docker-entrypoint.sh"]

EXPOSE 5678/tcp
```

----------------------------------------

TITLE: Troubleshooting Command Not Found in Running n8n Docker Container (Shell)
DESCRIPTION: This snippet provides shell commands to troubleshoot 'command not found' errors when n8n is running in a Docker container. It first helps identify the n8n container ID and then demonstrates how to execute a specific command within that running container to verify its availability and functionality.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.executecommand/common-issues.md#_snippet_0

LANGUAGE: sh
CODE:
```
# Find n8n's container ID, it will be the first column
docker ps | grep n8n
# Try to execute the command within the running container
docker container exec <container_ID> <command_to_run>
```

----------------------------------------

TITLE: Retrieve Last Item from Node - Python
DESCRIPTION: These methods retrieve data items from a specified upstream node. They allow for optional branchIndex and runIndex parameters to target specific outputs or workflow runs. When branchIndex is omitted, the method defaults to the output directly connecting the source node to the current node, making them suitable for use within the n8n Code node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/output-other-nodes.md#_snippet_9

LANGUAGE: Python
CODE:
```
_("<node-name>").last(branchIndex?, runIndex?)
```

----------------------------------------

TITLE: Updating One-Way Notion Relations with HTTP Request (JSON)
DESCRIPTION: This snippet demonstrates how to update a one-way Notion database relation using the HTTP Request node, as the native Notion node only supports two-way relations. It involves sending a PATCH request to the Notion API's pages endpoint with a JSON body specifying the relation ID.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.notion/common-issues.md#_snippet_0

LANGUAGE: URL
CODE:
```
https://api.notion.com/v1/pages/<page_id>
```

LANGUAGE: JSON
CODE:
```
{
	"properties": {
		"Account": {
			"relation": [
				{
					"id": "<your_relation_ID>"
				}
			]
		}
	}
}
```

----------------------------------------

TITLE: Formatting Dates for Default Human Readability in n8n (JavaScript)
DESCRIPTION: This snippet illustrates how to convert a Luxon DateTime object into a human-readable string using the `toLocaleString()` method without specific options. It applies a default locale-specific format (e.g., DD/MM/YYYY) to a calculated date, enhancing its presentation for users.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/luxon.md#_snippet_7

LANGUAGE: JavaScript
CODE:
```
{{$today.minus({days: 7}).toLocaleString()}}
```

LANGUAGE: JavaScript
CODE:
```
let readableSevenDaysAgo = $today.minus({days: 7}).toLocaleString()
```

----------------------------------------

TITLE: Alternative Extraction of City from Webhook Body (JavaScript)
DESCRIPTION: This JavaScript expression provides an alternative way to access the 'city' value from the incoming webhook body using bracket notation. It achieves the same result as the dot notation but can be useful for accessing properties with special characters or dynamic keys.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/expressions.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
{{$json['body']['city']}}
```

----------------------------------------

TITLE: Checking Node Execution Status in JavaScript
DESCRIPTION: This method allows checking whether a specific node within the workflow has already been executed. Replace `<node-name>` with the actual name of the node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/n8n-metadata.md#_snippet_6

LANGUAGE: JavaScript
CODE:
```
$("<node-name>").isExecuted
```

----------------------------------------

TITLE: Example n8n Configuration JSON File
DESCRIPTION: This JSON snippet provides an example structure for an n8n configuration file. It demonstrates how to configure execution data saving, timezone, and node exclusion, allowing granular control over n8n's behavior.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-methods.md#_snippet_4

LANGUAGE: json
CODE:
```
{
 "executions": {
  "saveDataOnSuccess": "none"
 },
 "generic": {
  "timezone": "Europe/Berlin"
 },
 "nodes": {
  "exclude": "[\"n8n-nodes-base.executeCommand\",\"n8n-nodes-base.writeBinaryFile\"]"
 }
}
```

----------------------------------------

TITLE: Example OAuth Credential Overwrite Data (JSON)
DESCRIPTION: This JSON snippet provides an example structure for `oauth-credentials.json`, which contains the `clientId` and `clientSecret` for overwriting Asana and GitHub OAuth2 API credentials. This data is used by the n8n backend for authentication without exposing it to users.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/embed/configuration.md#_snippet_1

LANGUAGE: json
CODE:
```
{
    "asanaOAuth2Api": {
        "clientId": "<id>",
        "clientSecret": "<secret>"
    },
    "githubOAuth2Api": {
        "clientId": "<id>",
        "clientSecret": "<secret>"
    }
}
```

----------------------------------------

TITLE: Setting n8n Instance Timezone in Bash
DESCRIPTION: This snippet demonstrates how to set the `GENERIC_TIMEZONE` environment variable using a bash command. This variable changes the default timezone for a self-hosted n8n instance from `America/New_York` to the specified value, affecting how time-based nodes like the Schedule node operate. The example sets the timezone to `Europe/Berlin`.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-examples/time-zone.md#_snippet_0

LANGUAGE: bash
CODE:
```
export GENERIC_TIMEZONE=Europe/Berlin
```

----------------------------------------

TITLE: Converting Array to JSON using AI Transform Node (Natural Language)
DESCRIPTION: This snippet demonstrates a natural language prompt used with the n8n AI Transform node to convert an array of data, specifically 'languages', into a JSON format with key-value pairs. This is useful for preparing data to be appended to Google Sheets.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.googlesheets/common-issues.md#_snippet_0

LANGUAGE: Natural Language
CODE:
```
Convert 'languages' array to JSON (key, value) pairs.
```

----------------------------------------

TITLE: Restoring Email with itemMatching in JavaScript
DESCRIPTION: This JavaScript code snippet, used within an n8n Code node, iterates through each input item to restore the original email address. It leverages the `itemMatching(i)` function to retrieve the corresponding item from the 'Customer Datastore (n8n training)' node, effectively linking current data to its historical source based on the item's index.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/builtin/itemmatching.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
for(let i=0; i<$input.all().length; i++) {
      $input.all()[i].json.restoreEmail = $('Customer Datastore (n8n training)').itemMatching(i).json.email;
}
return $input.all();
```

----------------------------------------

TITLE: Restoring Email with itemMatching in Python
DESCRIPTION: This Python code snippet, executed within an n8n Code node, iterates through each input item to restore the original email address. It utilizes the `itemMatching(i)` function to fetch the corresponding item from the 'Customer Datastore (n8n training)' node, linking current data to its historical source based on the item's index.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/builtin/itemmatching.md#_snippet_1

LANGUAGE: Python
CODE:
```
for i,item in enumerate(_input.all()):
      _input.all()[i].json.restoreEmail = _('Customer Datastore (n8n training)').itemMatching(i).json.email

return _input.all();
```

----------------------------------------

TITLE: Querying Postgres Server Host Address (SQL)
DESCRIPTION: This SQL query retrieves the IP address of the Postgres server. It is used to confirm the host or domain name required when configuring Postgres credentials in n8n.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/postgres.md#_snippet_0

LANGUAGE: SQL
CODE:
```
SELECT inet_server_addr();
```

----------------------------------------

TITLE: Accessing n8n Docker Shell for Specific Version (Shell)
DESCRIPTION: Access the interactive shell of the running n8n Docker container to manage a node's specific version.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/community-nodes/installation/manual-install.md#_snippet_7

LANGUAGE: shell
CODE:
```
docker exec -it n8n sh
```

----------------------------------------

TITLE: Enabling Prometheus Metrics Endpoint in n8n (Bash)
DESCRIPTION: This snippet demonstrates how to enable the Prometheus metrics endpoint in n8n by setting the `N8N_METRICS` environment variable to `true`. This action is a prerequisite for n8n to expose any of its internal metrics for monitoring purposes. It's typically executed in a shell environment before starting the n8n instance.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-examples/prometheus.md#_snippet_0

LANGUAGE: bash
CODE:
```
export N8N_METRICS=true
```

----------------------------------------

TITLE: Example Previous Node Output Data (JSON)
DESCRIPTION: This JSON array represents a typical output structure from an n8n node, containing multiple items, each with an 'id' and 'name' property. It serves as an example for demonstrating how to extract specific data points using expressions.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/data/data-mapping/data-mapping-expressions.md#_snippet_1

LANGUAGE: JSON
CODE:
```
[
  {
    "id": "23423532",
    "name": "Jay Gatsby"
  },
  {
    "id": "23423533",
    "name": "José Arcadio Buendía"
  },
  {
    "id": "23423534",
    "name": "Max Sendak"
  },
  {
    "id": "23423535",
    "name": "Zaphod Beeblebrox"
  },
  {
    "id": "23423536",
    "name": "Edmund Pevensie"
  }
]
```

----------------------------------------

TITLE: Calculating Days to Christmas using n8n Expressions (JavaScript)
DESCRIPTION: This snippet demonstrates how to calculate and display the number of days remaining until Christmas using an n8n expression. It leverages n8n's `$today` variable, Luxon's `DateTime.fromISO` and `diff` methods, `toObject()`, JMESPath (`.days`), and JavaScript string manipulation (`toString().substring(1)`) to format the output. The expression dynamically adjusts for the current year.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/luxon.md#_snippet_10

LANGUAGE: JavaScript
CODE:
```
{{ "There are " + $today.diff(DateTime.fromISO($today.year + '-12-25'), 'days').toObject().days.toString().substring(1) + " days to Christmas!" }}
```

----------------------------------------

TITLE: Setting WEBHOOK_URL Environment Variable (Shell)
DESCRIPTION: This shell command sets the `WEBHOOK_URL` environment variable. The value should be replaced with the public URL provided by ngrok from the previous step. This environment variable is then used to configure the Redirect URL for OAuth2 credentials, allowing the local n8n instance to receive callbacks from GetResponse.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/getresponse.md#_snippet_1

LANGUAGE: sh
CODE:
```
export WEBHOOK_URL=<YOUR-NGROK-URL>
```

----------------------------------------

TITLE: Running Multiple Commands on Separate Lines in Bash
DESCRIPTION: This snippet shows how to execute multiple shell commands by placing each command on a new line. This method also runs commands sequentially. Here, it changes the directory to 'bin' and then lists its contents.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.executecommand/index.md#_snippet_1

LANGUAGE: bash
CODE:
```
cd bin
ls
```

----------------------------------------

TITLE: Setting Single Custom Execution Data in n8n Code Node
DESCRIPTION: This snippet illustrates how to set a single key-value pair as custom execution data within the Code node. This data is temporary and specific to the current workflow execution, allowing for dynamic storage of information.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/builtin/execution.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
// Set a single piece of custom execution data
$execution.customData.set("key", "value");
```

LANGUAGE: Python
CODE:
```
# Set a single piece of custom execution data
_execution.customData.set("key", "value");
```

----------------------------------------

TITLE: Example of 'Invalid value for content' Error Message
DESCRIPTION: This snippet displays a typical '400 Invalid value for content' error message encountered when the 'Prompt' input in the AI Agent node receives a null value instead of an expected string. This indicates a problem with how expressions resolve or with incoming data containing nulls.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent/common-issues.md#_snippet_0

LANGUAGE: Plain Text
CODE:
```
Internal error
Error: 400 Invalid value for 'content': expected a string, got null.
<stack-trace>
```

----------------------------------------

TITLE: Counting Previous Node Items in Python
DESCRIPTION: This Python snippet is designed for n8n workflows to determine the number of items received from the preceding node. It verifies if the first item's JSON content is empty, returning 0 if true, or the total item count otherwise. The result is structured as a JSON object containing a 'results' field.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/code-node/number-items-last-node.md#_snippet_1

LANGUAGE: Python
CODE:
```
if len(items[0].json) == 0:
		return [
			{
				"json": {
					"results": 0,
				}
			}
		]
	else:
		return [
			{
				"json": {
					"results": items.length,
				}
			}
		]
```

LANGUAGE: JSON
CODE:
```
[
	{
		"results": 8
	}
]
```

----------------------------------------

TITLE: Disabling Telemetry Events in n8n (Bash)
DESCRIPTION: This Bash command sets the N8N_DIAGNOSTICS_ENABLED environment variable to 'false', effectively opting out of sending telemetry events from n8n. This is a common method for self-hosted users to manage data collection and enhance privacy.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/privacy-security/privacy.md#_snippet_0

LANGUAGE: bash
CODE:
```
export N8N_DIAGNOSTICS_ENABLED=false
```

----------------------------------------

TITLE: Disabling the n8n API Playground (Bash)
DESCRIPTION: This snippet demonstrates how to disable the n8n API playground (Swagger UI) by setting the N8N_PUBLIC_API_SWAGGERUI_DISABLED environment variable to 'true'. Disabling the playground prevents access to the interactive API documentation interface.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/securing/disable-public-api.md#_snippet_1

LANGUAGE: bash
CODE:
```
export N8N_PUBLIC_API_SWAGGERUI_DISABLED=true
```

----------------------------------------

TITLE: Getting Execution Resume URL in JavaScript
DESCRIPTION: This method provides the webhook URL necessary to resume a workflow that is currently paused at a Wait node. It enables external systems to trigger workflow continuation.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/n8n-metadata.md#_snippet_4

LANGUAGE: JavaScript
CODE:
```
$execution.resumeUrl
```

----------------------------------------

TITLE: Deleting All Kubernetes Resources
DESCRIPTION: This command deletes all Kubernetes resources (deployments, services, etc.) that were created by the manifest files in the current directory. It's used to remove the entire n8n and Postgres setup from the cluster.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/aws.md#_snippet_8

LANGUAGE: shell
CODE:
```
kubectl delete -f .
```

----------------------------------------

TITLE: Configuring Basic Authentication for n8n Credentials (TypeScript)
DESCRIPTION: This snippet shows how to set up Basic Authentication. The 'auth' property within the 'authenticate' object requires 'username' and 'password' fields, which n8n will use to construct the standard Basic Auth header.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/credentials-files.md#_snippet_4

LANGUAGE: typescript
CODE:
```
authenticate: IAuthenticateGeneric = {
	type: 'generic',
	properties: {
		auth: {
			username: '={{$credentials.username}}',
			password: '={{$credentials.password}}',
		},
	},
};
```

----------------------------------------

TITLE: Adding Custom Embed Fields to Discord Messages (Raw JSON)
DESCRIPTION: This JSON object demonstrates how to add custom fields like `footer` and `fields` to a Discord embed when using the n8n Discord node with 'Raw JSON' input method. It includes an author, URL, a regular field, and footer text with an icon URL, providing a flexible way to extend embed capabilities beyond default options.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.discord/common-issues.md#_snippet_0

LANGUAGE: json
CODE:
```
{
    "author": "My Name",
	"url": "https://discord.js.org",
	"fields": [
		{
			"name": "Regular field title",
			"value": "Some value here"
		}
	],
	"footer": {
		"text": "Some footer text here",
		"icon_url": "https://i.imgur.com/AfFp7pu.png"
	}
}
```

----------------------------------------

TITLE: Fetching n8n Workflow JSON via REST API (HTTP)
DESCRIPTION: This snippet demonstrates how to retrieve the JSON representation of a specific n8n workflow using a GET request to the n8n REST API. It requires the n8n domain and the unique workflow ID. The expected output is a JSON object containing the workflow's configuration.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/embed/managing-workflows.md#_snippet_1

LANGUAGE: HTTP
CODE:
```
GET https://<n8n-domain>/rest/workflows/1012
```

----------------------------------------

TITLE: Running n8n in Docker with Host Mapping (n8n Only, Linux)
DESCRIPTION: This command runs the n8n container interactively, removes it on exit, maps `host.docker.internal` to the host's gateway for network access, publishes port 5678, and mounts a volume for n8n data. This configuration enables n8n to connect to a locally hosted Ollama instance when n8n is running in Docker on Linux.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmchatollama/common-issues.md#_snippet_1

LANGUAGE: shell
CODE:
```
docker run -it --rm --add-host host.docker.internal:host-gateway --name n8n -p 5678:5678 -v n8n_data:/home/<USER>/.n8n docker.n8n.io/n8nio/n8n
```

----------------------------------------

TITLE: Error Data Structure for n8n Trigger Node Errors (JSON)
DESCRIPTION: This JSON object illustrates the data structure received by the n8n Error Trigger when an error originates specifically from the trigger node of the main workflow. It provides less `execution` information and more detailed `trigger` context, including error name, cause, timestamp, and node details.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_snippets/integrations/builtin/core-nodes/error-trigger/error-data.md#_snippet_1

LANGUAGE: json
CODE:
```
{
  "trigger": {
    "error": {
      "context": {},
      "name": "WorkflowActivationError",
      "cause": {
        "message": "",
        "stack": ""
      },
      "timestamp": 1654609328787,
      "message": "",
      "node": {
        ". . . "
      }
    },
    "mode": "trigger"
  },
  "workflow": {
    "id": "",
    "name": ""
  }
}
```

----------------------------------------

TITLE: Disabling n8n Features via Environment Variables - Shell
DESCRIPTION: This snippet shows how to disable n8n's default features like diagnostics, version notifications, and workflow templates by setting their respective environment variables to 'false'. This prevents the n8n instance from connecting to n8n's servers for these purposes.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-examples/isolation.md#_snippet_0

LANGUAGE: Shell
CODE:
```
N8N_DIAGNOSTICS_ENABLED=false
N8N_VERSION_NOTIFICATIONS_ENABLED=false
N8N_TEMPLATES_ENABLED=false
```

----------------------------------------

TITLE: Defining a Color Selector in n8n Node Parameters (TypeScript)
DESCRIPTION: This snippet illustrates how to implement a color parameter type in an n8n node. It provides a color selector UI element, initially empty. The displayOptions property dictates the visibility of this parameter based on specific resources and operations.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/ui-elements.md#_snippet_7

LANGUAGE: typescript
CODE:
```
{
	displayName: 'Background Color',
	name: 'backgroundColor',
	type: 'color',
	default: '', // Initially selected color
	displayOptions: { // the resources and operations to display this element with
		show: {
			resource: [
				// comma-separated list of resource names
			],
			operation: [
				// comma-separated list of operation names
			]
		}
	},
}
```

----------------------------------------

TITLE: Accessing Current Timestamp in n8n Expressions (JavaScript)
DESCRIPTION: This snippet demonstrates how to access the `$now` Luxon object within n8n expressions. When `$now` is used directly, it displays an ISO formatted timestamp. However, when concatenated with a string, it is cast to a Unix timestamp, illustrating different string representations based on context.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/luxon.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
{{$now}}
// n8n displays the ISO formatted timestamp
// For example 2022-03-09T14:02:37.065+00:00
{{"Today's date is " + $now}}
// n8n displays "Today's date is <unix timestamp>"
// For example "Today's date is 1646834498755"
```

----------------------------------------

TITLE: Providing Multiple RSS Feed URLs in n8n Code Node (JavaScript)
DESCRIPTION: This JavaScript snippet, used within an n8n Code node, initializes an array of JSON objects, each containing a 'url' property. These URLs represent different RSS feed sources that the subsequent 'Loop Over Items' and 'RSS Feed Read' nodes will process. It serves as the initial data input for a workflow designed to read multiple RSS feeds.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.splitinbatches.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
return [
	{
		json: {
			url: 'https://medium.com/feed/n8n-io',
		}
	},
	{
		json: {
			url: 'https://dev.to/feed/n8n',
		}
	}
];
```

----------------------------------------

TITLE: Accessing Execution ID in n8n Workflows
DESCRIPTION: This snippet demonstrates how to retrieve the unique identifier for the current workflow execution. The `execution.id` property provides a distinct ID for each workflow run, which is useful for logging, tracking, or referencing specific executions.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/builtin/execution.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
let executionId = $execution.id;
```

LANGUAGE: Python
CODE:
```
executionId = _execution.id
```

----------------------------------------

TITLE: Example Webhook Node JSON Input Structure
DESCRIPTION: This JSON structure represents the typical input received from a webhook node in n8n, containing nested data for 'people' (an array of objects) and 'dogs' (an object of objects). This data serves as the basis for applying JMESPath expressions.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/jmespath.md#_snippet_3

LANGUAGE: JSON
CODE:
```
[
  {
    "headers": {
      "host": "n8n.instance.address",
      ...
    },
    "params": {},
    "query": {},
    "body": {
      "people": [
        {
          "first": "James",
          "last": "Green"
        },
        {
          "first": "Jacob",
          "last": "Jones"
        },
        {
          "first": "Jayden",
          "last": "Smith"
        }
      ],
      "dogs": {
        "Fido": {
          "color": "brown",
          "age": 7
        },
        "Spot": {
          "color": "black and white",
          "age": 5
        }
      }
    }
  }
]
```

----------------------------------------

TITLE: Updating Docker Container Permissions for n8n v1.0 (Bash)
DESCRIPTION: This command updates file permissions within an n8n Docker container, changing ownership from `root` to `node:node` for the `~/.n8n` directory. This is necessary for Docker-based deployments of n8n v1.0, as the n8n process now runs as the `node` user for increased security. Execute this command on the Docker host if permission errors appear in container logs after updating to ensure proper operation.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/1-0-migration-checklist.md#_snippet_0

LANGUAGE: bash
CODE:
```
docker run --rm -it --user root -v ~/.n8n:/home/<USER>/.n8n --entrypoint chown n8nio/base:16 -R node:node /home/<USER>/.n8n
```

----------------------------------------

TITLE: Listing MySQL Databases - SQL
DESCRIPTION: This SQL query lists all available databases on the MySQL server. It helps in confirming the correct database name to be used when setting up the n8n credential.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/mysql.md#_snippet_1

LANGUAGE: SQL
CODE:
```
SHOW DATABASES;
```

----------------------------------------

TITLE: Example Webhook Node JSON Input
DESCRIPTION: This JSON object represents typical input data from a webhook node, containing nested 'people' and 'dogs' objects. It serves as the source for subsequent data extraction examples.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/jmespath.md#_snippet_7

LANGUAGE: js
CODE:
```
[
  {
    "headers": {
      "host": "n8n.instance.address",
      ...
    },
    "params": {},
    "query": {},
    "body": {
      "people": [
        {
          "first": "James",
          "last": "Green"
        },
        {
          "first": "Jacob",
          "last": "Jones"
        },
        {
          "first": "Jayden",
          "last": "Smith"
        }
      ],
      "dogs": {
        "Fido": {
          "color": "brown",
          "age": 7
        },
        "Spot": {
          "color": "black and white",
          "age": 5
        }
      }
    }
  }
]
```

----------------------------------------

TITLE: Setting n8n Configuration File Path (PowerShell)
DESCRIPTION: This PowerShell command demonstrates how to set the `N8N_CONFIG_FILES` environment variable to specify a single JSON configuration file for n8n. The `User` scope ensures the variable persists for the current user.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-methods.md#_snippet_3

LANGUAGE: powershell
CODE:
```
[Environment]::SetEnvironmentVariable('N8N_CONFIG_FILES', '<path-to-config>\config.json', 'User')
```

----------------------------------------

TITLE: Set n8n Environment Variables for S3 Connection
DESCRIPTION: Configure these environment variables in your n8n environment to provide the connection details for your S3 bucket, including the host, bucket name, region, access key ID, and secret access key.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/external-storage.md#_snippet_1

LANGUAGE: sh
CODE:
```
export N8N_EXTERNAL_STORAGE_S3_HOST=... # example: s3.us-east-1.amazonaws.com
export N8N_EXTERNAL_STORAGE_S3_BUCKET_NAME=...
export N8N_EXTERNAL_STORAGE_S3_BUCKET_REGION=...
export N8N_EXTERNAL_STORAGE_S3_ACCESS_KEY=...
export N8N_EXTERNAL_STORAGE_S3_ACCESS_SECRET=...
```

----------------------------------------

TITLE: Setting n8n Timezone in Docker
DESCRIPTION: This command starts n8n in a Docker container and configures its timezone. It sets both the GENERIC_TIMEZONE for n8n's internal scheduling and the system-wide TZ environment variable to 'Europe/Berlin', ensuring consistent timezone behavior.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/docker.md#_snippet_2

LANGUAGE: sh
CODE:
```
docker volume create n8n_data

docker run -it --rm \
 --name n8n \
 -p 5678:5678 \
 -e GENERIC_TIMEZONE="Europe/Berlin" \
 -e TZ="Europe/Berlin" \
 -v n8n_data:/home/<USER>/.n8n \
 docker.n8n.io/n8nio/n8n
```

----------------------------------------

TITLE: Creating an EKS Cluster with eksctl (Shell)
DESCRIPTION: This command uses the `eksctl` CLI tool to create a new Amazon EKS Kubernetes cluster. It requires a specified cluster name (e.g., 'n8n') and an AWS region. This process can take a significant amount of time to complete, after which `eksctl` automatically configures `kubectl` to connect to the new cluster.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/aws.md#_snippet_0

LANGUAGE: shell
CODE:
```
eksctl create cluster --name n8n --region <your-aws-region>
```

----------------------------------------

TITLE: Configuring n8n Logging with Environment Variables - Bash
DESCRIPTION: This snippet demonstrates how to configure n8n's logging behavior using environment variables. It covers setting the log level to 'debug', directing output to both console and a file, specifying the log file location, and defining maximum file size and count for log rotation. These variables control the verbosity and storage of n8n logs.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/logging-monitoring/logging.md#_snippet_0

LANGUAGE: bash
CODE:
```
# Set the logging level to 'debug'
export N8N_LOG_LEVEL=debug

# Set log output to both console and a log file
export N8N_LOG_OUTPUT=console,file

# Set a save location for the log file
export N8N_LOG_FILE_LOCATION=/home/<USER>/n8n/logs/n8n.log

# Set a 50 MB maximum size for each log file
export N8N_LOG_FILE_MAXSIZE=50

# Set 60 as the maximum number of log files to be kept
export N8N_LOG_FILE_MAXCOUNT=60
```

----------------------------------------

TITLE: Configuring Custom Auth Body (JSON)
DESCRIPTION: Shows how to define data to be sent in the request body using the body property in the Custom Auth credential configuration. This is useful for sending data like usernames and passwords in the request payload.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/httprequest.md#_snippet_1

LANGUAGE: JSON
CODE:
```
{
	 "body" : {
		"user": "username",
		"pass": "password"
	}
}
```

----------------------------------------

TITLE: Configuring Query String Authentication for n8n Credentials (TypeScript)
DESCRIPTION: This snippet illustrates how to send authentication data as part of the URL's query string. The 'qs' property within the 'authenticate' object maps a credential property like 'token' to a query parameter, useful for simple API key authentication.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/credentials-files.md#_snippet_3

LANGUAGE: typescript
CODE:
```
authenticate: IAuthenticateGeneric = {
	type: 'generic',
	properties: {
		qs: {
			token: '={{$credentials.token}}',
		},
	},
};
```

----------------------------------------

TITLE: Cloning n8n Docker Caddy Repository - Shell
DESCRIPTION: This command clones the n8n-docker-caddy configuration repository from GitHub into the root user folder of the server. This repository contains essential Docker Compose, n8n, and Caddy configuration files required for the setup.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/hetzner.md#_snippet_1

LANGUAGE: shell
CODE:
```
git clone https://github.com/n8n-io/n8n-docker-caddy.git
```

----------------------------------------

TITLE: Trace Linked Item in Code Node - JavaScript
DESCRIPTION: Designed specifically for the n8n Code node, this method serves as a robust alternative to the .item property for tracing linked items. By providing a currentNodeInputIndex, it enables precise traceback from an input item to its originating item in an upstream node, facilitating complex data manipulation within code.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/output-other-nodes.md#_snippet_6

LANGUAGE: JavaScript
CODE:
```
$("<node-name>").itemMatching(currentNodeInputIndex)
```

----------------------------------------

TITLE: Handling Input Data in n8n Node Execute Method (TypeScript)
DESCRIPTION: This snippet highlights how an n8n node retrieves input data using `getInputData()` and iterates through it to process each item. It shows how `getNodeParameter` is used within the loop to access parameters specific to each input item, enabling the node to handle multiple inputs from previous nodes.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/programmatic-style-node.md#_snippet_9

LANGUAGE: TypeScript
CODE:
```
const items = this.getInputData();
... 
for (let i = 0; i < items.length; i++) {
	...n	const email = this.getNodeParameter('email', i) as string;
	...
}
```

----------------------------------------

TITLE: Upgrading a Community Node to Latest via npm (Shell)
DESCRIPTION: Update a previously installed community node package to its latest version available on npm. Replace n8n-nodes-nodeName with the actual package name.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/community-nodes/installation/manual-install.md#_snippet_6

LANGUAGE: shell
CODE:
```
npm update n8n-nodes-nodeName
```

----------------------------------------

TITLE: Generating New Items in n8n Code Node (Linked with pairedItem)
DESCRIPTION: This JavaScript snippet refines the previous example by adding the `pairedItem` property to each newly generated output item. By setting `pairedItem` to the index `i`, it establishes a direct link between the new item and its original input item, enabling proper data traceability within the n8n workflow.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_snippets/data/data-mapping/item-linking-code-node.md#_snippet_3

LANGUAGE: JavaScript
CODE:
```
newItems = [];
for(let i=0; i<items.length; i++){
  newItems.push(
    {
      "json":
        {
          "name": items[i].json.name,
					"aBrandNewField": "New data for item " + i
        },
      "pairedItem": i
    }    
  )
}
return newItems;
```

----------------------------------------

TITLE: Access Node Parameters - Python
DESCRIPTION: This property returns an object containing the configuration and query settings of the specified node. It includes details such as the operation performed by the node and any result limits applied, offering insight into how the upstream node processed data. This property is fully supported within the n8n Code node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/output-other-nodes.md#_snippet_11

LANGUAGE: Python
CODE:
```
_("<node-name>").params
```

----------------------------------------

TITLE: Generating New Items in n8n Code Node (Unlinked)
DESCRIPTION: This JavaScript snippet demonstrates how to process input items and generate new output items in an n8n Code node. It iterates through the input `items` array, extracts the 'name', and adds a new field, but crucially, it does not include `pairedItem`, resulting in unlinked output items.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_snippets/data/data-mapping/item-linking-code-node.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
newItems = [];
for(let i=0; i<items.length; i++){
  newItems.push(
    {
    "json":
      {
        "name": items[i].json.name,
				"aBrandNewField": "New data for item " + i
      }
    }
  )
}

return newItems;
```

----------------------------------------

TITLE: Disabling Version Update Notifications in n8n (Bash)
DESCRIPTION: This Bash command sets the N8N_VERSION_NOTIFICATIONS_ENABLED environment variable to 'false', preventing n8n from checking for and notifying about new software versions. This helps control external network requests and provides more control over updates.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/privacy-security/privacy.md#_snippet_1

LANGUAGE: bash
CODE:
```
export N8N_VERSION_NOTIFICATIONS_ENABLED=false
```

----------------------------------------

TITLE: Installing a Specific Node Version via npm (Shell)
DESCRIPTION: Install a specific version of a community node package from npm. Replace n8n-nodes-nodeName with the package name and 2.1.0 with the desired version number.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/community-nodes/installation/manual-install.md#_snippet_9

LANGUAGE: shell
CODE:
```
# Replace 2.1.0 with your version number
npm install n8n-nodes-nodeName@2.1.0
```

----------------------------------------

TITLE: Constructing Postbin Content Expression in n8n (JavaScript)
DESCRIPTION: This expression is used within the n8n Postbin node to dynamically generate the content sent to Postbin. It combines a static string with the value of the 'classType' property from the incoming JSON data, retrieved using the '{{$json["classType"]}}' syntax. This allows for customized messages based on the solar flare classification.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/try-it-out/tutorial-first-workflow.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
There was a solar flare of class {{$json["classType"]}}
```

----------------------------------------

TITLE: Checking Execution Mode in Python
DESCRIPTION: This method indicates whether the workflow execution was triggered automatically or manually. Possible return values are `test` and `production`.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/n8n-metadata.md#_snippet_22

LANGUAGE: Python
CODE:
```
_execution.mode
```

----------------------------------------

TITLE: Importing Credentials from File - n8n CLI
DESCRIPTION: This command imports credentials from a specified JSON file into n8n. It is useful for migrating or restoring specific credential configurations.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_17

LANGUAGE: bash
CODE:
```
n8n import:credentials --input=file.json
```

----------------------------------------

TITLE: Configuring HTTP Request Node for PokéAPI (n8n Workflow Configuration)
DESCRIPTION: This configuration sets up an HTTP Request node to fetch data from the PokéAPI. It performs a GET request to retrieve a list of Pokémon, serving as the input for subsequent XML conversion.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-2.md#_snippet_2

LANGUAGE: JSON
CODE:
```
{
  "nodeType": "n8n-nodes-base.httpRequest",
  "parameters": {
    "authentication": "none",
    "requestMethod": "GET",
    "url": "https://pokeapi.co/api/v2/pokemon"
  }
}
```

----------------------------------------

TITLE: Supported Scopes for Google Cloud Storage (API Configuration)
DESCRIPTION: These are the specific OAuth2 scopes required for integrating Google Cloud Storage with n8n. They provide various levels of access, from full control to read-only, for managing objects and buckets in Cloud Storage.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/google/oauth-generic.md#_snippet_8

LANGUAGE: API Configuration
CODE:
```
https://www.googleapis.com/auth/cloud-platform
```

LANGUAGE: API Configuration
CODE:
```
https://www.googleapis.com/auth/cloud-platform.read-only
```

LANGUAGE: API Configuration
CODE:
```
https://www.googleapis.com/auth/devstorage.full_control
```

LANGUAGE: API Configuration
CODE:
```
https://www.googleapis.com/auth/devstorage.read_only
```

LANGUAGE: API Configuration
CODE:
```
https://www.googleapis.com/auth/devstorage.read_write
```

----------------------------------------

TITLE: Enabling Concurrency Control in n8n (Shell)
DESCRIPTION: This snippet enables concurrency control for self-hosted n8n instances by setting the N8N_CONCURRENCY_PRODUCTION_LIMIT environment variable. It limits the number of concurrent production executions to 20, queuing any additional executions until capacity becomes available. This helps prevent performance degradation due to too many simultaneous executions.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/concurrency-control.md#_snippet_0

LANGUAGE: sh
CODE:
```
export N8N_CONCURRENCY_PRODUCTION_LIMIT=20
```

----------------------------------------

TITLE: Supported Scopes for Google BigQuery (API Configuration)
DESCRIPTION: This is the specific OAuth2 scope required for integrating Google BigQuery with n8n. It grants comprehensive access to BigQuery datasets and operations, allowing for data manipulation and querying.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/google/oauth-generic.md#_snippet_4

LANGUAGE: API Configuration
CODE:
```
https://www.googleapis.com/auth/bigquery
```

----------------------------------------

TITLE: Accessing Specific Custom Execution Data in n8n Code Node
DESCRIPTION: This snippet illustrates how to retrieve a specific value from the custom execution data object using its corresponding key. This provides targeted access to individual pieces of data stored during the current workflow execution.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/builtin/execution.md#_snippet_4

LANGUAGE: JavaScript
CODE:
```
// Access a specific value set during this execution
var customData = $execution.customData.get("key")
```

LANGUAGE: Python
CODE:
```
# Access a specific value set during this execution
customData = _execution.customData.get("key")
```

----------------------------------------

TITLE: Example Output of Extracted CSV Data as JSON in n8n
DESCRIPTION: This JSON snippet demonstrates the typical output structure generated by the 'Extract From File' node when it processes a CSV file. Each row from the input CSV is transformed into a JSON object, where individual column values are accessible through numerical keys nested within a 'row' object.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.extractfromfile.md#_snippet_0

LANGUAGE: JSON
CODE:
```
{
  "row": {
  "0": "apple",
  "1": "1",
  "2": "2",
  "3": "3"
  }
  ...
}
```

----------------------------------------

TITLE: Setting LangSmith Environment Variables for n8n
DESCRIPTION: This snippet details the environment variables required to connect a self-hosted n8n instance to LangSmith. These variables enable tracing and specify the LangSmith API endpoint and API key. They must be set globally in the environment where n8n is hosted, not within n8n's configuration file, and n8n must be restarted after setting them.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/advanced-ai/langchain/langsmith.md#_snippet_0

LANGUAGE: Environment Variables
CODE:
```
LANGCHAIN_ENDPOINT="https://api.smith.langchain.com"
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=<YOUR_API_KEY>
```

----------------------------------------

TITLE: S3 Bucket Policy for n8n Binary Data
DESCRIPTION: This JSON policy grants n8n the necessary permissions to interact with the specified S3 bucket for storing and retrieving binary data. Remember to replace <bucket-name> with your actual bucket name.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/external-storage.md#_snippet_0

LANGUAGE: json
CODE:
```
{
 "Version": "2012-10-17",
 "Statement": [
  {
   "Sid": "VisualEditor0",
   "Effect": "Allow",
   "Action": ["s3:*"],
   "Resource": ["arn:aws:s3:::<bucket-name>", "arn:aws:s3:::<bucket-name>/*"]
  }
 ]
}
```

----------------------------------------

TITLE: Disabling Telemetry Events in n8n (Bash)
DESCRIPTION: This snippet shows how to disable telemetry event collection in n8n by setting the `N8N_DIAGNOSTICS_ENABLED` environment variable to `false`. This prevents anonymous data related to events from being sent to n8n servers, enhancing user privacy.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/securing/telemetry-opt-out.md#_snippet_0

LANGUAGE: bash
CODE:
```
export N8N_DIAGNOSTICS_ENABLED=false
```

----------------------------------------

TITLE: Configuring n8n Item Linking with pairedItem in JavaScript
DESCRIPTION: This snippet demonstrates the basic structure for manually linking an output item to an input item in n8n's Code node. By setting `pairedItem` to the index of the original input item, you establish a traceable connection, allowing subsequent nodes to access preceding data.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_snippets/data/data-mapping/item-linking-code-node.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
[
	{
		"json": {
			. . . 
		},
		// The index of the input item that generated this output item
		"pairedItem": 0
	}
]
```

----------------------------------------

TITLE: Boolean Data Transformation Functions in n8n JavaScript
DESCRIPTION: This snippet lists a new JavaScript function for transforming boolean data within n8n workflows. The `toInt()` function converts a boolean value to its integer representation (e.g., true to 1, false to 0).
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/release-notes.md#_snippet_6

LANGUAGE: JavaScript
CODE:
```
toInt()
```

----------------------------------------

TITLE: Configuring Nginx for MCP Server Trigger Node
DESCRIPTION: This Nginx location block configures a reverse proxy to properly handle Server-Sent Events (SSE) for the n8n MCP Server Trigger node. It disables proxy buffering, gzip compression, and chunked transfer encoding, and clears the 'Connection' header to prevent issues with persistent connections.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-langchain.mcptrigger.md#_snippet_1

LANGUAGE: Nginx
CODE:
```
location /mcp/ {
    proxy_http_version          1.1;
    proxy_buffering             off;
    gzip                        off;
    chunked_transfer_encoding   off;

    proxy_set_header            Connection '';

    # The rest of your proxy headers and settings
    # . . .
}
```

----------------------------------------

TITLE: Exporting All n8n Credentials to a Directory for Backup
DESCRIPTION: This command exports all n8n credentials to a specified directory using the `--backup` flag, which automatically enables `--all`, `--pretty`, and `--separate` for organized backups. The `--output` flag specifies the target directory.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_13

LANGUAGE: bash
CODE:
```
n8n export:credentials --backup --output=backups/latest/
```

----------------------------------------

TITLE: Configuring If Node for Conditional Logic in n8n (JSON)
DESCRIPTION: This snippet defines an If node in n8n, used for conditional branching. It checks if the 'region' field from the input JSON equals 'Americas' with case-sensitive strict validation.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-5/chapter-5.2.md#_snippet_4

LANGUAGE: JSON
CODE:
```
{
    "parameters": {
        "conditions": {
        "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
        },
        "conditions": [
            {
            "id": "d3afe65c-7c80-4caa-9d1c-33c62fbc2197",
            "leftValue": "={{ $json.region }}",
            "rightValue": "Americas",
            "operator": {
                "type": "string",
                "operation": "equals",
                "name": "filter.operator.equals"
            }
            }
        ],
        "combinator": "and"
        },
        "options": {}
    },
    "id": "2ed874a9-5bcf-4cc9-9b52-ea503a562892",
    "name": "If",
    "type": "n8n-nodes-base.if",
    "typeVersion": 2,
    "position": [
        1660,
        500
    ]
}
```

----------------------------------------

TITLE: Accessing All Custom Execution Data (Code Node)
DESCRIPTION: This snippet illustrates how to retrieve the entire custom data object associated with the current n8n workflow execution using the Code node. This provides access to all custom metadata set for the execution.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/workflows/executions/custom-executions-data.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
const customData = $execution.customData.getAll();
```

LANGUAGE: Python
CODE:
```
customData = _execution.customData.getAll();
```

----------------------------------------

TITLE: Disabling Version Notifications in n8n (Bash)
DESCRIPTION: This snippet demonstrates how to prevent n8n from checking for new versions by setting the `N8N_VERSION_NOTIFICATIONS_ENABLED` environment variable to `false`. This stops periodic communication with n8n servers for version updates, providing more control over outbound connections.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/securing/telemetry-opt-out.md#_snippet_1

LANGUAGE: bash
CODE:
```
export N8N_VERSION_NOTIFICATIONS_ENABLED=false
```

----------------------------------------

TITLE: Accessing Current Timestamp in n8n Code Node (Python)
DESCRIPTION: This snippet illustrates how to access the `_now` variable, which is the Python equivalent of `$now`, within an n8n Python Code node. Directly using `_now` displays an ISO formatted timestamp. Explicitly converting `_now` to a string using `str()` results in a Unix timestamp representation.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/luxon.md#_snippet_2

LANGUAGE: Python
CODE:
```
_now
# n8n displays <ISO formatted timestamp>
# For example 2022-03-09T14:00:25.058+00:00
rightNow = "Today's date is " + str(_now)
# n8n displays "Today's date is <unix timestamp>"
# For example "Today's date is 1646834498755"
```

----------------------------------------

TITLE: Disabling the n8n Public REST API (Bash)
DESCRIPTION: This snippet shows how to disable the n8n public REST API by setting the N8N_PUBLIC_API_DISABLED environment variable to 'true'. Disabling the API enhances the security of your n8n installation if you do not plan to use it programmatically.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/securing/disable-public-api.md#_snippet_0

LANGUAGE: bash
CODE:
```
export N8N_PUBLIC_API_DISABLED=true
```

----------------------------------------

TITLE: Configuring Header Authentication for n8n Credentials (TypeScript)
DESCRIPTION: This snippet demonstrates sending authentication data in the request header. It uses the 'header' property within the 'authenticate' object to define a 'Bearer' token using the 'authToken' credential property, commonly used for OAuth 2.0 or API key authentication.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/credentials-files.md#_snippet_2

LANGUAGE: typescript
CODE:
```
authenticate: IAuthenticateGeneric = {
	type: 'generic',
	properties: {
		header: {
			Authorization: '=Bearer {{$credentials.authToken}}',
		},
	},
};
```

----------------------------------------

TITLE: Activating Credential Overwrite Endpoint (Shell)
DESCRIPTION: This snippet demonstrates how to activate a custom REST endpoint for credential overwrites by setting the `CREDENTIALS_OVERWRITE_ENDPOINT` environment variable. This endpoint will be used to load sensitive credential data into n8n.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/embed/configuration.md#_snippet_0

LANGUAGE: sh
CODE:
```
export CREDENTIALS_OVERWRITE_ENDPOINT=send-credentials
```

----------------------------------------

TITLE: Iterating and Accessing Data from Retrieved Node Items
DESCRIPTION: This snippet illustrates how to iterate through the items returned by the `all()` method and access their underlying JSON data. It highlights the specific conversion needed for Python items to be used as dictionaries.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/builtin/all.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
previousNodeData = $("<node-name>").all();
for(let i=0; i<previousNodeData.length; i++) {
	console.log(previousNodeData[i].json);
}
```

LANGUAGE: Python
CODE:
```
previousNodeData = _("<node-name>").all();
for item in previousNodeData:
	# item is of type <class 'pyodide.ffi.JsProxy'>
	# You need to convert it to a Dict
	  itemDict = item.json.to_py()
	  print(itemDict)
```

----------------------------------------

TITLE: Accessing n8n Environment Variables in JavaScript
DESCRIPTION: This snippet demonstrates how to access a user-defined variable within the n8n environment using the `$vars` object in JavaScript. Variables are read-only and must be set via the n8n UI.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/builtin/vars.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
// Access a variable
$vars.<variable-name>
```

----------------------------------------

TITLE: Setting Node Subtitle in n8n (JavaScript)
DESCRIPTION: This snippet demonstrates how to dynamically set a node's subtitle in n8n using an expression. The subtitle combines the values of the 'operation' and 'resource' parameters, providing a clear summary of the node's configuration. This helps users quickly understand the node's purpose without opening its details.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/plan/node-ui-design.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
subtitle: '={{$parameter["operation"] + ": " + $parameter["resource"]}}',
```

----------------------------------------

TITLE: Scheduling Weekdays at 9 AM with n8n Schedule Trigger (Cron)
DESCRIPTION: This Cron expression schedules a workflow to run at 9:00 AM, Monday through Friday. The `1-5` in the day-of-week field specifies the range of weekdays for execution.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger/index.md#_snippet_7

LANGUAGE: Cron
CODE:
```
0 9 * * 1-5
```

----------------------------------------

TITLE: Customizing UI Text with Vue I18n in n8n (JavaScript)
DESCRIPTION: This snippet demonstrates how to white-label n8n's UI text by modifying the `en.json` internationalization file. It introduces a new `_brand.name` key and links existing messages to it, allowing for centralized brand name changes. This uses Vue I18n's linked locale messages feature.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/embed/white-labelling.md#_snippet_5

LANGUAGE: JavaScript
CODE:
```
{
	"_brand.name": "My Brand",
	"about.aboutN8n": "About @:_brand.name",
	"about.n8nVersion": "@:_brand.name Version"
}
```

----------------------------------------

TITLE: Filtering KoboToolbox Submissions with MongoDB Query (JSON)
DESCRIPTION: This JSON object demonstrates how to filter KoboToolbox submissions using MongoDB's query format. It filters for submissions with a 'success' status and a submission time before November 1st, 2021, 01:02:03. This helps in retrieving specific subsets of data based on multiple criteria.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.kobotoolbox.md#_snippet_0

LANGUAGE: JSON
CODE:
```
{"status": "success", "_submission_time": {"$lt": "2021-11-01T01:02:03"}}
```

----------------------------------------

TITLE: Scheduling Custom Hourly Range with n8n Schedule Trigger (Cron)
DESCRIPTION: This Cron expression schedules a workflow to run every hour between 9:00 AM and 5:00 PM daily. The `9-17` in the hour field defines the inclusive range for hourly execution.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger/index.md#_snippet_8

LANGUAGE: Cron
CODE:
```
0 9-17 * * *
```

----------------------------------------

TITLE: Accessing All Custom Execution Data in n8n Code Node
DESCRIPTION: This snippet demonstrates how to retrieve the complete custom execution data object as it currently stands. This allows access to all key-value pairs that have been stored during the ongoing workflow execution within the Code node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/builtin/execution.md#_snippet_3

LANGUAGE: JavaScript
CODE:
```
// Access the current state of the object during the execution
var customData = $execution.customData.getAll()
```

LANGUAGE: Python
CODE:
```
# Access the current state of the object during the execution
customData = _execution.customData.getAll()
```

----------------------------------------

TITLE: Creating a New User on DigitalOcean Droplet (Shell)
DESCRIPTION: This command creates a new user account on the DigitalOcean Droplet. It prompts for user details like password and full name. This step is crucial for security, avoiding direct work as the root user.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/digital-ocean.md#_snippet_0

LANGUAGE: shell
CODE:
```
adduser <username>
```

----------------------------------------

TITLE: Cloning n8n Kubernetes Hosting Repository (Shell)
DESCRIPTION: This command clones the `n8n-kubernetes-hosting` Git repository from GitHub, specifically checking out the `aws` branch. This repository contains the necessary Kubernetes configuration files (YAML manifests) required for deploying n8n and its dependencies on AWS EKS.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/aws.md#_snippet_1

LANGUAGE: shell
CODE:
```
git clone https://github.com/n8n-io/n8n-kubernetes-hosting.git -b aws
```

----------------------------------------

TITLE: Creating an Array of Contact Objects in n8n Code Node (JavaScript)
DESCRIPTION: This snippet demonstrates how to initialize an array of contact objects (`myContacts`) within an n8n Code node. Each object contains a `name` and a nested `email` object with `personal` and `work` email addresses, showcasing how to structure complex JSON data for output.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-1.md#_snippet_3

LANGUAGE: JavaScript
CODE:
```
var myContacts = [
	{
		json: {
			name: 'Alice',
			email: {
				personal: '<EMAIL>',
				work: '<EMAIL>'
			},
		}
	},
	{
		json: {
			name: 'Bob',
			email: {
				personal: '<EMAIL>',
				work: '<EMAIL>'
				},
		}
	},
];

return myContacts;
```

----------------------------------------

TITLE: Configuring Persistent Volume Claim for n8n Deployment (YAML)
DESCRIPTION: This YAML snippet defines a persistent volume claim named 'n8n-claim0' within the 'volumes' section of the n8n deployment manifest. This is crucial for nodes interacting with files and for persisting encryption keys across restarts, ensuring data integrity and availability.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/google-cloud.md#_snippet_3

LANGUAGE: yaml
CODE:
```
volumes:
  - name: n8n-claim0
    persistentVolumeClaim:
      claimName: n8n-claim0
```

----------------------------------------

TITLE: Opening n8n Environment File for Configuration - Shell
DESCRIPTION: This command opens the `.env` file in the `nano` text editor. This file contains environment variables that need to be configured for the n8n application running within its Docker container, allowing users to customize settings like domain and credentials.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/hetzner.md#_snippet_7

LANGUAGE: shell
CODE:
```
nano .env
```

----------------------------------------

TITLE: Running Ollama in Docker with Port Publishing (Ollama Only)
DESCRIPTION: This command runs the Ollama container in detached mode, mounts a volume for data persistence, and publishes port 11434 from the container to the host. This allows n8n to connect to Ollama when Ollama is the only component running in Docker.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmchatollama/common-issues.md#_snippet_0

LANGUAGE: shell
CODE:
```
docker run -d -v ollama:/root/.ollama -p 11434:11434 --name ollama ollama/ollama
```

----------------------------------------

TITLE: Defining n8n Node Metadata (JSON)
DESCRIPTION: This JSON snippet defines the metadata for an n8n node, `n8n-nodes-base.NasaPics`, in its codex file. It specifies the node and codex versions, categorizes the node under 'Miscellaneous', and includes placeholders for credential and primary documentation URLs. This file is essential for n8n to properly recognize and categorize the custom node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/declarative-style-node.md#_snippet_9

LANGUAGE: JSON
CODE:
```
{
	"node": "n8n-nodes-base.NasaPics",
	"nodeVersion": "1.0",
	"codexVersion": "1.0",
	"categories": [
		"Miscellaneous"
	],
	"resources": {
		"credentialDocumentation": [
			{
				"url": ""
			}
		],
		"primaryDocumentation": [
			{
				"url": ""
			}
		]
	}
}
```

----------------------------------------

TITLE: Creating Docker Volume for Caddy Data - Shell
DESCRIPTION: This command creates a Docker volume named `caddy_data`. This volume is used to persist the Caddy cache between container restarts, which helps speed up Caddy's start times and ensures data integrity.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/hetzner.md#_snippet_3

LANGUAGE: shell
CODE:
```
docker volume create caddy_data
```

----------------------------------------

TITLE: Stopping Docker Compose Services (Bash)
DESCRIPTION: This command gracefully stops all running Docker Compose services defined in the current directory's `compose.yaml` file. It ensures that containers are shut down properly.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/docker-compose.md#_snippet_10

LANGUAGE: bash
CODE:
```
sudo docker compose stop
```

----------------------------------------

TITLE: Installing Docker Compose Plugin - Shell
DESCRIPTION: This snippet updates the package list and upgrades existing packages, then installs the Docker Compose plugin on the server. This is necessary because the Hetzner Docker app image does not include Docker Compose by default.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/hetzner.md#_snippet_0

LANGUAGE: shell
CODE:
```
apt update && apt -y upgrade
apt install docker-compose-plugin
```

----------------------------------------

TITLE: Setting Up SendGrid API Key Authentication in n8n (TypeScript)
DESCRIPTION: This snippet defines the `FriendGridApi` class, implementing `ICredentialType` to configure API key authentication for SendGrid. It specifies the `apiKey` property for user input, sets up the `Authorization` header for generic authentication, and defines a test request to validate the credentials.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/programmatic-style-node.md#_snippet_10

LANGUAGE: TypeScript
CODE:
```
import {
	IAuthenticateGeneric,
	ICredentialTestRequest,
	ICredentialType,
	INodeProperties,
} from 'n8n-workflow';

export class FriendGridApi implements ICredentialType {
	name = 'friendGridApi';
	displayName = 'FriendGrid API';
	properties: INodeProperties[] = [
		{
			displayName: 'API Key',
			name: 'apiKey',
			type: 'string',
			default: '',
		},
	];

	authenticate: IAuthenticateGeneric = {
		type: 'generic',
		properties: {
			headers: {
				Authorization: '=Bearer {{$credentials.apiKey}}',
			},
		},
	};

	test: ICredentialTestRequest = {
		request: {
			baseURL: 'https://api.sendgrid.com/v3',
			url: '/marketing/contacts',
		},
	};
}
```

----------------------------------------

TITLE: Installing Project Dependencies (Shell)
DESCRIPTION: This command installs all required project dependencies for the n8n node development environment. It should be run after cloning the starter repository to ensure all necessary packages are available for building and testing the node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/declarative-style-node.md#_snippet_1

LANGUAGE: shell
CODE:
```
npm i
```

----------------------------------------

TITLE: Configuring Node Description Properties (TypeScript)
DESCRIPTION: This TypeScript snippet adds essential configuration properties to the node's `description` object. It defines the node's display name, internal name, icon path, group, version, dynamic subtitle, general description, default name, input/output types, required credentials, and default request settings including the base URL and headers for API calls.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/declarative-style-node.md#_snippet_4

LANGUAGE: typescript
CODE:
```
displayName: 'NASA Pics',
name: 'NasaPics',
icon: 'file:nasapics.svg',
group: ['transform'],
version: 1,
subtitle: '={{$parameter["operation"] + ": " + $parameter["resource"]}}',
description: 'Get data from NASAs API',
defaults: {
	name: 'NASA Pics',
},
inputs: ['main'],
outputs: ['main'],
credentials: [
	{
		name: 'NasaPicsApi',
		required: true,
	},
],
requestDefaults: {
	baseURL: 'https://api.nasa.gov',
	headers: {
		Accept: 'application/json',
		'Content-Type': 'application/json',
	},
},
```

----------------------------------------

TITLE: Specifying Google OAuth Scopes for Domain-Wide Delegation
DESCRIPTION: This snippet provides an example of comma-separated OAuth 2.0 scopes required when configuring domain-wide delegation for a Google Service Account. These scopes define the permissions granted to the service account, allowing it to impersonate users and access specific Google APIs like Drive and Calendar on their behalf.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/google/service-account.md#_snippet_0

LANGUAGE: Text
CODE:
```
https://www.googleapis.com/auth/drive, https://www.googleapis.com/auth/calendar
```

----------------------------------------

TITLE: Managing Node-Specific Static Data in JavaScript
DESCRIPTION: This snippet shows how to retrieve, access, update, and delete static data specific to a single node in an n8n workflow using JavaScript. Node static data is unique to the node that set it and is automatically saved if changed upon successful workflow execution. It's useful for storing small, node-specific persistent information.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/builtin/get-workflow-static-data.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
// Get the static data of the node
const nodeStaticData = $getWorkflowStaticData('node');

// Access its data
const lastExecution = nodeStaticData.lastExecution;

// Update its data
nodeStaticData.lastExecution = new Date().getTime();

// Delete data
delete nodeStaticData.lastExecution;
```

----------------------------------------

TITLE: General Syntax for Data Transformation Functions (JavaScript)
DESCRIPTION: This snippet illustrates the general syntax for calling data transformation functions within n8n expressions. These functions are invoked on a `dataItem` using dot notation, followed by the function name and parentheses, allowing for various data manipulations.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/data-transformation-functions/index.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
{{ dataItem.function() }}
```

----------------------------------------

TITLE: Example Current Node Input Data (JSON)
DESCRIPTION: This JSON array illustrates a typical input data structure for an n8n node, similar to the output example, containing items with 'id' and 'name'. It's used to show how expressions can extract data from the current node's input.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/data/data-mapping/data-mapping-expressions.md#_snippet_4

LANGUAGE: JSON
CODE:
```
[
  {
    "id": "23423532",
    "name": "Jay Gatsby"
  },
  {
    "id": "23423533",
    "name": "José Arcadio Buendía"
  },
  {
    "id": "23423534",
    "name": "Max Sendak"
  },
  {
    "id": "23423535",
    "name": "Zaphod Beeblebrox"
  },
  {
    "id": "23423536",
    "name": "Edmund Pevensie"
  }
]
```

----------------------------------------

TITLE: Outline Structure for a Declarative-style n8n Node (JavaScript)
DESCRIPTION: This snippet provides the basic class structure for a declarative-style n8n node. It imports necessary interfaces like `INodeType` and `INodeTypeDescription` from `n8n-workflow` and defines the `description` object, which contains the node's basic details and properties for resources and operations. Declarative nodes handle data processing via the `routing` key in `properties`.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/node-base-files/structure.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import { INodeType, INodeTypeDescription } from 'n8n-workflow';

export class ExampleNode implements INodeType {
	description: INodeTypeDescription = {
		// Basic node details here
		properties: [
			// Resources and operations here
		]
	};
}
```

----------------------------------------

TITLE: Processing Input with JavaScript in Custom Code Tool
DESCRIPTION: This JavaScript snippet demonstrates how to access the tool's input using the `query` variable and perform a simple string manipulation (lowercasing) before returning the result. It shows a basic example of processing data provided to the Custom Code Tool node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.toolcode.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
let myString = query;
return myString.toLowerCase();
```

----------------------------------------

TITLE: Configuring Loop Over Items Node in n8n (JSON)
DESCRIPTION: This snippet configures a 'Loop Over Items' node (Split In Batches) in n8n to process items in batches of 5. This is useful for handling large datasets efficiently.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-5/chapter-5.2.md#_snippet_7

LANGUAGE: JSON
CODE:
```
{
    "parameters": {
        "batchSize": 5,
        "options": {}
    },
    "id": "0fa1fbf6-fe77-4044-a445-c49a1db37dec",
    "name": "Loop Over Items",
    "type": "n8n-nodes-base.splitInBatches",
    "typeVersion": 3,
    "position": [
        1660,
        700
    ]
}
```

----------------------------------------

TITLE: Starting n8n with tunnel enabled (Bash)
DESCRIPTION: This command starts n8n and enables the '--tunnel' option, which is used to expose the local n8n instance to the internet, typically for testing webhooks or integrations that require public access.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/npm.md#_snippet_6

LANGUAGE: bash
CODE:
```
n8n start --tunnel
```

----------------------------------------

TITLE: Configuring Merge Node in n8n (JSON)
DESCRIPTION: This snippet defines a Merge node in n8n, configured to combine data based on matching 'customerID' fields. It specifies the merge mode, fields for merging, and node metadata.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-5/chapter-5.2.md#_snippet_2

LANGUAGE: JSON
CODE:
```
{
    "parameters": {
        "mode": "combine",
        "mergeByFields": {
        "values": [
            {
            "field1": "customerID",
            "field2": "customerID"
            }
        ]
        },
        "options": {}
    },
    "id": "1cddc984-7fca-45e0-83b8-0c502cb4c78c",
    "name": "Merge",
    "type": "n8n-nodes-base.merge",
    "typeVersion": 2.1,
    "position": [
        1220,
        600
    ]
}
```

----------------------------------------

TITLE: Triggering Webhook Node: Basic GET Request with cURL
DESCRIPTION: This snippet demonstrates how to trigger an n8n Webhook node using a basic GET request via cURL, without any additional parameters. It's useful for verifying basic connectivity and webhook setup.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.webhook/common-issues.md#_snippet_0

LANGUAGE: sh
CODE:
```
curl --request GET <https://your-n8n.url/webhook/path>
```

----------------------------------------

TITLE: Granting Sudo Privileges to a User on DigitalOcean Droplet (Shell)
DESCRIPTION: This command adds the specified user to the 'sudo' group, granting them administrative privileges. This allows the user to run commands with superuser permissions by prefixing them with 'sudo', enhancing security and system management.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/digital-ocean.md#_snippet_1

LANGUAGE: shell
CODE:
```
usermod -aG sudo <username>
```

----------------------------------------

TITLE: Extracting Name from Linked Item in Current Node Input (JavaScript)
DESCRIPTION: This expression shows how to extract the 'name' property from a linked input item within the current node. While data mapping is often used, this expression provides an alternative for direct access to specific data points from the input.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/data/data-mapping/data-mapping-expressions.md#_snippet_5

LANGUAGE: JavaScript
CODE:
```
{{$input.item.json.name}}
```

----------------------------------------

TITLE: Exporting All n8n Credentials to Standard Output
DESCRIPTION: This command exports all existing n8n credentials directly to the terminal's standard output. It is useful for quickly viewing or piping credential data without saving it to a file.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_10

LANGUAGE: bash
CODE:
```
n8n export:credentials --all
```

----------------------------------------

TITLE: Setting n8n Configuration File Path (Bash)
DESCRIPTION: This snippet illustrates how to define the path to n8n's JSON configuration files using the `N8N_CONFIG_FILES` environment variable in a Bash shell. It supports both single and multiple comma-separated file paths for flexible configuration management.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-methods.md#_snippet_2

LANGUAGE: shell
CODE:
```
# Bash - Single file
export N8N_CONFIG_FILES=/<path-to-config>/my-config.json
# Bash - Multiple files are comma-separated
export N8N_CONFIG_FILES=/<path-to-config>/my-config.json,/<path-to-config>/production.json
```

----------------------------------------

TITLE: Setting n8n User Folder Path using Bash
DESCRIPTION: This snippet demonstrates how to set the N8N_USER_FOLDER environment variable in a Bash shell. This variable allows users to specify a custom location for n8n to store user-specific data, overriding the default .n8n subfolder in the user's home directory. This is useful for managing data storage or for specific deployment scenarios.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-examples/user-folder.md#_snippet_0

LANGUAGE: bash
CODE:
```
export N8N_USER_FOLDER=/home/<USER>/n8n
```

----------------------------------------

TITLE: Importing Workflows from File - n8n CLI
DESCRIPTION: This command imports a single workflow from a specified JSON file into n8n. It is typically used for restoring or migrating individual workflows.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_15

LANGUAGE: bash
CODE:
```
n8n import:workflow --input=file.json
```

----------------------------------------

TITLE: Exporting All n8n Credentials in Decrypted Plain Text
DESCRIPTION: This command exports all n8n credentials in a plain text, decrypted format to a specified file. This is useful for migrating credentials between installations with different secret keys but exposes sensitive information in the output file.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_14

LANGUAGE: bash
CODE:
```
n8n export:credentials --all --decrypted --output=backups/decrypted.json
```

----------------------------------------

TITLE: Triggering Webhook Node: GET Request with Body Parameter using cURL
DESCRIPTION: This cURL command sends a GET request to the n8n Webhook node, including a simple key-value pair as a body parameter. This is useful for testing how the webhook processes data sent in the request body.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.webhook/common-issues.md#_snippet_1

LANGUAGE: sh
CODE:
```
curl --request GET <https://your-n8n.url/webhook/path> --data 'key=value'
```

----------------------------------------

TITLE: Getting Execution Resume URL in Python
DESCRIPTION: This method provides the webhook URL necessary to resume a workflow that is currently paused at a Wait node. It enables external systems to trigger workflow continuation.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/n8n-metadata.md#_snippet_23

LANGUAGE: Python
CODE:
```
_execution.resumeUrl
```

----------------------------------------

TITLE: Configuring Basic n8n Node Properties (TypeScript)
DESCRIPTION: This snippet defines essential properties within the `description` object of an n8n node. It sets the display name, internal name, icon path, group, version, a brief description, default values, input/output capabilities, and required credentials, which are used by the n8n Editor UI for rendering and functionality.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/programmatic-style-node.md#_snippet_4

LANGUAGE: typescript
CODE:
```
displayName: 'FriendGrid',
name: 'friendGrid',
icon: 'file:friendGrid.svg',
group: ['transform'],
version: 1,
description: 'Consume SendGrid API',
defaults: {
	name: 'FriendGrid',
},
inputs: ['main'],
outputs: ['main'],
credentials: [
	{
		name: 'friendGridApi',
		required: true,
	},
],
```

----------------------------------------

TITLE: Cloning n8n Docker Caddy Configuration Repository (Shell)
DESCRIPTION: This command clones the n8n-docker-caddy GitHub repository into the current directory. This repository contains essential Docker Compose, n8n, and Caddy configuration files required for setting up n8n on the Droplet.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/installation/server-setups/digital-ocean.md#_snippet_2

LANGUAGE: shell
CODE:
```
git clone https://github.com/n8n-io/n8n-docker-caddy.git
```

----------------------------------------

TITLE: Configuring Collection UI Element for Optional Fields in n8n (TypeScript)
DESCRIPTION: This snippet shows how to use the `collection` type to display optional fields within the n8n UI. It defines a `placeholder` and an `options` array, where each object in the array represents an optional field, such as a 'Type' dropdown with predefined values. This allows for dynamic addition of fields by the user.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/ui-elements.md#_snippet_4

LANGUAGE: TypeScript
CODE:
```
{
	displayName: 'Filters',
	name: 'filters',
	type: 'collection',
	placeholder: 'Add Field',
	default: {},
	options: [
		{
			displayName: 'Type',
			name: 'type',
			type: 'options',
			options: [
				{
					name: 'Automated',
					value: 'automated',
				},
				{
					name: 'Past',
					value: 'past',
				},
				{
					name: 'Upcoming',
					value: 'upcoming',
				},
			],
			default: '',
		},
	],
	displayOptions: { // the resources and operations to display this element with
		show: {
			resource: [
				// comma-separated list of resource names
			],
			operation: [
				// comma-separated list of operation names
			]
		}
	},
}
```

----------------------------------------

TITLE: Importing Core n8n and Request Types (TypeScript)
DESCRIPTION: These import statements bring in essential interfaces and types from `n8n-core`, `n8n-workflow`, and `request` libraries. These types are fundamental for defining the structure, execution logic, and HTTP request capabilities of an n8n programmatic node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/programmatic-style-node.md#_snippet_2

LANGUAGE: typescript
CODE:
```
import {
	IExecuteFunctions,
} from 'n8n-core';

import {
	IDataObject,
	INodeExecutionData,
	INodeType,
	INodeTypeDescription,
} from 'n8n-workflow';

import {
	OptionsWithUri,
} from 'request';
```

----------------------------------------

TITLE: Access Node Parameters - JavaScript
DESCRIPTION: This property returns an object containing the configuration and query settings of the specified node. It includes details such as the operation performed by the node and any result limits applied, offering insight into how the upstream node processed data. This property is fully supported within the n8n Code node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/output-other-nodes.md#_snippet_4

LANGUAGE: JavaScript
CODE:
```
$("<node-name>").params
```

----------------------------------------

TITLE: Example Input Data for MySQL Query - JavaScript
DESCRIPTION: This JavaScript snippet illustrates the structure of input data typically used with the n8n MySQL node. It shows an array of objects, each representing a record with 'email', 'name', and 'age' fields, which can be processed iteratively in a workflow.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.mysql/index.md#_snippet_0

LANGUAGE: js
CODE:
```
[
    {
        "email": "<EMAIL>",
        "name": "Alex",
        "age": 21 
    },
    {
        "email": "<EMAIL>",
        "name": "Jamie",
        "age": 33 
    }
]
```

----------------------------------------

TITLE: Slicing First Names with JMESPath Slice Projection
DESCRIPTION: This snippet demonstrates how to use JMESPath slice projections to extract a subset of 'first' names from the 'people' array. It shows implementation in both n8n expressions and Code nodes for JavaScript and Python, returning an array containing the first two names.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/jmespath.md#_snippet_5

LANGUAGE: JavaScript
CODE:
```
{{$jmespath($json.body.people, "[:2].first")}}
// Returns ["James", "Jacob"]
```

LANGUAGE: JavaScript
CODE:
```
let firstTwoNames = $jmespath($json.body.people, "[:2].first");
return {firstTwoNames};
/* Returns:
[
	{
		"firstTwoNames": [
			"James",
			"Jacob"
		]
	}
]
*/
```

LANGUAGE: Python
CODE:
```
firstTwoNames = _jmespath(_json.body.people, "[:2].first" )
return {"firstTwoNames":firstTwoNames}
"""
Returns:
[
  	{
		"firstTwoNames": [
		"James",
		"Jacob"
		]
	}
]
"""
```

----------------------------------------

TITLE: Adding Optional Fields for NASA APOD in n8n Node (TypeScript)
DESCRIPTION: This snippet demonstrates how to add an optional 'Date' field for the Astronomy Picture of the Day (APOD) endpoint. It uses a 'collection' type to group additional fields under a collapsible section in the UI. The 'displayOptions' ensure this field is only shown when the 'astronomyPictureOfTheDay' resource and 'get' operation are selected. The 'routing' object appends the selected date as a query string parameter.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/declarative-style-node.md#_snippet_7

LANGUAGE: TypeScript
CODE:
```
{
	displayName: 'Additional Fields',
	name: 'additionalFields',
	type: 'collection',
	default: {},
	placeholder: 'Add Field',
	displayOptions: {
		show: {
			resource: [
				'astronomyPictureOfTheDay',
			],
			operation: [
				'get',
			],
		},
	},
	options: [
		{
			displayName: 'Date',
			name: 'apodDate',
			type: 'dateTime',
			default: '',
			routing: {
				request: {
					// You've already set up the URL. qs appends the value of the field as a query string
					qs: {
						date: '={{ new Date($value).toISOString().substr(0,10) }}',
					},
				},
			},
		},
	],					
}
```

----------------------------------------

TITLE: Executing a Saved n8n Workflow by ID
DESCRIPTION: This command allows you to directly start a specific n8n workflow using its unique ID from the command line interface. Replace `<ID>` with the actual ID of the workflow you wish to execute.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_1

LANGUAGE: bash
CODE:
```
n8n execute --id <ID>
```

----------------------------------------

TITLE: Creating and Navigating to Custom Nodes Directory (Shell)
DESCRIPTION: Create the directory ~/.n8n/nodes if it does not exist and change the current directory to it, preparing for node installation.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/community-nodes/installation/manual-install.md#_snippet_1

LANGUAGE: shell
CODE:
```
mkdir ~/.n8n/nodes
cd ~/.n8n/nodes
```

----------------------------------------

TITLE: Configuring Node Metadata for FriendGrid Node (JSON)
DESCRIPTION: This JSON snippet defines the metadata for the `FriendGrid` n8n node, including its internal name, versioning, and categorization. It also provides placeholders for credential and primary documentation URLs, essential for n8n's internal registry and user interface.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/programmatic-style-node.md#_snippet_11

LANGUAGE: JSON
CODE:
```
{
	"node": "n8n-nodes-base.FriendGrid",
	"nodeVersion": "1.0",
	"codexVersion": "1.0",
	"categories": [
		"Miscellaneous"
	],
	"resources": {
		"credentialDocumentation": [
			{
				"url": ""
			}
		],
		"primaryDocumentation": [
			{
				"url": ""
			}
		]
	}
}
```

----------------------------------------

TITLE: Enabling n8n Execution Data Pruning (Docker)
DESCRIPTION: This snippet demonstrates how to enable and configure automatic data pruning for n8n when running via Docker. It sets environment variables within the `docker run` command to activate pruning and define the maximum age of execution data.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/execution-data.md#_snippet_4

LANGUAGE: sh
CODE:
```
# Docker
docker run -it --rm \
 --name n8n \
 -p 5678:5678 \
 -e EXECUTIONS_DATA_PRUNE=true \
 -e EXECUTIONS_DATA_MAX_AGE=168 \
 docker.n8n.io/n8nio/n8n
```

----------------------------------------

TITLE: Valid Single-line JavaScript Expression (Luxon)
DESCRIPTION: This JavaScript snippet demonstrates a valid single-line expression in n8n using the Luxon library. It calculates the difference in months between two ISO-formatted dates by chaining method calls, adhering to the single-line constraint for n8n expressions.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/expressions.md#_snippet_4

LANGUAGE: JavaScript
CODE:
```
{{DateTime.fromISO('2017-03-13').diff(DateTime.fromISO('2017-02-13'), 'months').toObject()}}
```

----------------------------------------

TITLE: Perform JMESPath Search with $jmespath() in JavaScript
DESCRIPTION: The `$jmespath()` method is an n8n-provided utility for executing JMESPath queries on JSON objects. It is exclusively available for use within the n8n Code node, allowing users to efficiently extract or transform data based on JMESPath expressions.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/jmespath.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
$jmespath()
```

----------------------------------------

TITLE: Scheduling Every 10 Seconds with n8n Schedule Trigger (Cron)
DESCRIPTION: This Cron expression schedules a workflow to run every 10 seconds. It utilizes the sixth asterisk for seconds, setting it to `*/10` to indicate execution at intervals of 10 seconds.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger/index.md#_snippet_0

LANGUAGE: Cron
CODE:
```
*/10 * * * * *
```

----------------------------------------

TITLE: Defining Credential Test Request in n8n (TypeScript)
DESCRIPTION: This snippet defines the structure for testing credentials. The 'test' object contains a 'request' property with 'baseURL' and 'url' fields, which n8n uses to make a test API call to verify if the provided credentials are valid. The 'baseURL' can dynamically reference credential properties.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/credentials-files.md#_snippet_5

LANGUAGE: typescript
CODE:
```
test: ICredentialTestRequest = {
		request: {
			baseURL: '={{$credentials?.domain}}',
			url: '/bearer',
		},
	};
```

----------------------------------------

TITLE: Checking Remaining Items in n8n Node (Expression)
DESCRIPTION: This expression checks if an n8n node, specifically 'Loop Over Items', has finished processing all its items. It returns `true` if no items are left, and `false` if there are still items to process. This is useful for conditional logic in workflows.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.splitinbatches.md#_snippet_1

LANGUAGE: n8n Expression Language
CODE:
```
{{$node["Loop Over Items"].context["noItemsLeft"]}}
```

----------------------------------------

TITLE: Example Nested Input Data for UI Mapping - JavaScript
DESCRIPTION: This JavaScript object demonstrates a more complex input data structure containing nested properties. It's used to explain how n8n displays and allows mapping of nested data in its UI, specifically showing how 'nested' fields are handled in table view.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/data/data-mapping/data-mapping-ui.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
[
  {
    "name": "First item",
    "nested": {
      "example-number-field": 1,
      "example-string-field": "apples"
    }
  },
  {
    "name": "Second item",
    "nested": {
      "example-number-field": 2,
      "example-string-field": "oranges"
    }
  }
]
```

----------------------------------------

TITLE: Running MySQL in Docker with Port Publishing - Shell
DESCRIPTION: This shell command runs a MySQL Docker container, publishing its default port 3306 to the host machine's port 3306. This configuration is used when only MySQL is running in Docker, allowing n8n (running directly on the host) to connect to the MySQL server via localhost.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.mysql/common-issues.md#_snippet_1

LANGUAGE: Shell
CODE:
```
docker run -p 3306:3306 --name my-mysql -d mysql:latest
```

----------------------------------------

TITLE: Using Static SQL IN Clause in Postgres
DESCRIPTION: This snippet demonstrates a basic SQL `IN` clause in Postgres, used to filter results based on a static list of values. It's a fundamental way to compare a column against multiple possible values.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.postgres/common-issues.md#_snippet_0

LANGUAGE: SQL
CODE:
```
SELECT color, shirt_size FROM shirts WHERE shirt_size IN ('small', 'medium', 'large');
```

----------------------------------------

TITLE: Triggering Webhook Node: Sending a File via GET Request with cURL
DESCRIPTION: This cURL command demonstrates how to send a file as part of a GET request to the n8n Webhook node. The `--form` option is used to specify the file path, allowing for testing of file uploads or attachments via the webhook.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.webhook/common-issues.md#_snippet_3

LANGUAGE: sh
CODE:
```
curl --request GET <https://your-n8n.url/webhook/path> --from 'key=@/path/to/file'
```

----------------------------------------

TITLE: Installing Local n8n Node into n8n Instance (Shell)
DESCRIPTION: This command installs your locally linked custom node into your n8n installation. It must be run from the n8n nodes directory (e.g., `~/.n8n/custom/`) and uses the package name defined in your node's `package.json` to create a symlink to your local node.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_snippets/integrations/creating-nodes/testing.md#_snippet_2

LANGUAGE: shell
CODE:
```
npm link <node-package-name>
```

----------------------------------------

TITLE: Demonstrating Multiple Matches Option in n8n
DESCRIPTION: This JSON example provides two identical datasets with duplicate entries, used to illustrate the 'Multiple Matches' option in the n8n Compare Datasets node. It shows how 'Include All Matches' returns all matching items, while 'Include First Match Only' returns only the first occurrence of each unique match.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.comparedatasets.md#_snippet_1

LANGUAGE: json
CODE:
```
// Input 1
[
	{
		"fruit": {
			"type": "apple",
			"color": "red"
		}
	},
				{
		"fruit": {
			"type": "apple",
			"color": "red"
		}
	},
				{
		"fruit": {
			"type": "banana",
			"color": "yellow"
		}
	}
]
// Input 2
[
	{
		"fruit": {
			"type": "apple",
			"color": "red"
		}
	},
				{
		"fruit": {
			"type": "apple",
			"color": "red"
		}
	},
				{
		"fruit": {
			"type": "banana",
			"color": "yellow"
		}
	}
]
```

----------------------------------------

TITLE: Defining Main n8n Node Class Structure (TypeScript)
DESCRIPTION: This code defines the main class `FriendGrid` which implements the `INodeType` interface, a requirement for all n8n programmatic nodes. It includes a `description` property for node metadata and an `execute` method placeholder where the node's core logic will reside. The class name must match the file name.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/programmatic-style-node.md#_snippet_3

LANGUAGE: typescript
CODE:
```
export class FriendGrid implements INodeType {
	description: INodeTypeDescription = {
		// Basic node details will go here
		properties: [
			// Resources and operations will go here
		],
	};
	// The execute method will go here
	async execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
	}
}
```

----------------------------------------

TITLE: n8n Workflow: Convert JSON to Binary File and Read/Write
DESCRIPTION: This n8n workflow demonstrates how to fetch JSON data from the Poetry DB API, convert it into a binary file using the 'Convert to File' node, write this file to a temporary location on disk, and then read it back using the 'Read/Write Files from Disk' node. This serves as a complete solution for handling JSON as binary data within n8n.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-2.md#_snippet_7

LANGUAGE: JSON
CODE:
```
{
	"name": "JSON to file and Read-Write",
	"nodes": [
		{
		"parameters": {},
		"id": "78639a25-b69a-4b9c-84e0-69e045bed1a3",
		"name": "When clicking \"Execute Workflow\"",
		"type": "n8n-nodes-base.manualTrigger",
		"typeVersion": 1,
		"position": [
			480,
			520
		]
		},
		{
		"parameters": {
			"url": "https://poetrydb.org/random/1",
			"options": {}
		},
		"id": "a11310df-1287-4e9a-b993-baa6bd4265a6",
		"name": "HTTP Request",
		"type": "n8n-nodes-base.httpRequest",
		"typeVersion": 4.1,
		"position": [
			680,
			520
		]
		},
		{
		"parameters": {
			"operation": "toJson",
			"options": {}
		},
		"id": "06be18f6-f193-48e2-a8d9-35f4779d8324",
		"name": "Convert to File",
		"type": "n8n-nodes-base.convertToFile",
		"typeVersion": 1,
		"position": [
			880,
			520
		]
		},
		{
		"parameters": {
			"operation": "write",
			"fileName": "/tmp/poetrydb.json",
			"options": {}
		},
		"id": "f2048e5d-fa8f-4708-b15a-d07de359f2e5",
		"name": "Read/Write Files from Disk",
		"type": "n8n-nodes-base.readWriteFile",
		"typeVersion": 1,
		"position": [
			1080,
			520
		]
		},
		{
		"parameters": {
			"fileSelector": "={{ $json.fileName }}",
			"options": {}
		},
		"id": "d630906c-09d4-49f4-ba14-416c0f4de1c8",
		"name": "Read/Write Files from Disk1",
		"type": "n8n-nodes-base.readWriteFile",
		"typeVersion": 1,
		"position": [
			1280,
			520
		]
		}
	],
	"pinData": {},
	"connections": {
		"When clicking \"Execute Workflow\"": {
		"main": [
			[
			{
				"node": "HTTP Request",
				"type": "main",
				"index": 0
			}
			]
		]
		},
		"HTTP Request": {
		"main": [
			[
			{
				"node": "Convert to File",
				"type": "main",
				"index": 0
			}
			]
		]
		},
		"Convert to File": {
		"main": [
			[
			{
				"node": "Read/Write Files from Disk",
				"type": "main",
				"index": 0
			}
			]
		]
		},
		"Read/Write Files from Disk": {
		"main": [
			[
			{
				"node": "Read/Write Files from Disk1",
				"type": "main",
				"index": 0
			}
			]
		]
		}
	}
}
```

----------------------------------------

TITLE: Configuring Number UI Element with Decimal Precision in n8n (TypeScript)
DESCRIPTION: This snippet demonstrates configuring a number UI element with specific constraints. It includes `maxValue`, `minValue`, and `numberPrecision` within `typeOptions` to control the input range and decimal places. Other standard properties like `displayName`, `name`, `type`, `required`, `default`, `description`, and `displayOptions` are also present.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/ui-elements.md#_snippet_3

LANGUAGE: TypeScript
CODE:
```
{
	displayName: 'Amount',
	name: 'amount',
	type: 'number',
	required: true,
	typeOptions: {
		maxValue: 10,
		minValue: 0,
		numberPrecision: 2,
	},
	default: 10.00,
	description: 'Your current amount',
	displayOptions: { // the resources and operations to display this element with
		show: {
			resource: [
				// comma-separated list of resource names
			],
			operation: [
				// comma-separated list of operation names
			]
		}
	},
}
```

----------------------------------------

TITLE: Configuring n8n Execution Data Saving (npm)
DESCRIPTION: This snippet shows how to configure n8n to reduce the amount of execution data saved when running with npm. It allows specifying whether to save data for errors, successful executions, node progress, and manual executions using environment variables.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/execution-data.md#_snippet_0

LANGUAGE: sh
CODE:
```
# npm
# Save executions ending in errors
export EXECUTIONS_DATA_SAVE_ON_ERROR=all

# Save successful executions
export EXECUTIONS_DATA_SAVE_ON_SUCCESS=all

# Don't save node progress for each execution
export EXECUTIONS_DATA_SAVE_ON_PROGRESS=false

# Don't save manually launched executions
export EXECUTIONS_DATA_SAVE_MANUAL_EXECUTIONS=false
```

----------------------------------------

TITLE: Updating package.json for n8n Node Configuration (JSON)
DESCRIPTION: This JSON snippet provides the structure for the `package.json` file required for an n8n community node. It defines metadata such as name, version, and description, and critically includes the `n8n` object which links to the node's credentials and main node file. Users must update fields like `name`, `author`, and `repository.url` to reflect their specific project details.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/programmatic-style-node.md#_snippet_12

LANGUAGE: json
CODE:
```
{
	"name": "n8n-nodes-friendgrid",
	"version": "0.1.0",
	"description": "n8n node to create contacts in SendGrid",
	"keywords": [
		"n8n-community-node-package"
	],
	"license": "MIT",
	"homepage": "https://n8n.io",
	"author": {
		"name": "Test",
		"email": "<EMAIL>"
	},
	"repository": {
		"type": "git",
		"url": "git+<your-repo-url>"
	},
	"main": "index.js",
	"scripts": {
	},
	"files": [
		"dist"
	],
	"n8n": {
		"n8nNodesApiVersion": 1,
		"credentials": [
			"dist/credentials/FriendGridApi.credentials.js"
		],
		"nodes": [
			"dist/nodes/FriendGrid/FriendGrid.node.js"
		]
	},
	"devDependencies": {
	},
	"peerDependencies": {
	}
}
```

----------------------------------------

TITLE: Defining the Main Node Class Structure (TypeScript)
DESCRIPTION: This TypeScript snippet defines the `NasaPics` class, which implements the `INodeType` interface. It establishes the basic structure for an n8n node, including a `description` property that will hold metadata and an empty `properties` array for defining node parameters and operations.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/declarative-style-node.md#_snippet_3

LANGUAGE: typescript
CODE:
```
export class NasaPics implements INodeType {
	description: INodeTypeDescription = {
		// Basic node details will go here
		properties: [
		// Resources and operations will go here
		]
	};
}
```

----------------------------------------

TITLE: Configuring Resource Mapper UI Element in n8n Node (JavaScript)
DESCRIPTION: This snippet demonstrates how to define a 'resourceMapper' type UI element within an n8n node's options. It configures the display name, internal name, default mapping mode, and various `typeOptions` for the resource mapper, including the method for fetching schema, operation mode, field labels, and support for automatic mapping and multi-key matching. This setup allows users to map input data directly within the node's UI.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/ui-elements.md#_snippet_14

LANGUAGE: JavaScript
CODE:
```
{
	displayName: 'Columns',
	name: 'columns', // The name used to reference the element UI within the code
	type: 'resourceMapper', // The UI element type
	default: {
		// mappingMode can be defined in the component (mappingMode: 'defineBelow')
		// or you can attempt automatic mapping (mappingMode: 'autoMapInputData')
		mappingMode: 'defineBelow',
		// Important: always set default value to null
		value: null,
	},
	required: true,
	// See "Resource mapper type options interface" below for the full typeOptions specification
	typeOptions: {
		resourceMapper: {
			resourceMapperMethod: 'getMappingColumns',
			mode: 'update',
			fieldWords: {
				singular: 'column',
				plural: 'columns',
			},
			addAllFields: true,
			multiKeyMatch: true,
			supportAutoMap: true,
			matchingFieldsLabels: {
				title: 'Custom matching columns title',
				description: 'Help text for custom matching columns',
				hint: 'Below-field hint for custom matching columns',
			},
		},
	},
},
```

----------------------------------------

TITLE: Accessing Workflow Static Data in Python
DESCRIPTION: This method provides access to static workflow data. Note that static data does not persist during workflow testing; it requires the workflow to be active and triggered by a trigger or webhook to be saved.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/n8n-metadata.md#_snippet_24

LANGUAGE: Python
CODE:
```
_getWorkflowStaticData(type)
```

----------------------------------------

TITLE: Constructing Discord Message with n8n Expression
DESCRIPTION: This n8n expression dynamically generates the message content sent to a Discord channel. It retrieves 'totalBooked' and 'bookedSum' values from the workflow's current JSON data and extracts a unique ID from the 'HTTP Request' node's header parameters. This allows for automated, data-rich notifications.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-one/chapter-5/chapter-5.6.md#_snippet_0

LANGUAGE: n8n Expression
CODE:
```
This week we've {{$json["totalBooked"]}} booked orders with a total value of {{$json["bookedSum"]}}. My Unique ID: {{ $('HTTP Request').params["headerParameters"]["parameters"][0]["value"] }}
```

----------------------------------------

TITLE: Accessing Linked Item from Current Node Input in n8n Expressions (JavaScript)
DESCRIPTION: This expression retrieves the linked input item that the current node associates with an output item. It is used when item linking occurs within the node itself, returning the entire linked input item object.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/data/data-mapping/data-mapping-expressions.md#_snippet_3

LANGUAGE: JavaScript
CODE:
```
{{$input.item}}
```

----------------------------------------

TITLE: Enabling TheHive 5 Notifications via cURL
DESCRIPTION: This cURL command enables event notifications in TheHive by updating the organization's notification configuration. It registers the previously defined webhook endpoints (`TESTING_WEBHOOK_NAME` and `PRODUCTION_WEBHOOK_NAME`) to respond to 'AnyEvent' triggers.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/trigger-nodes/n8n-nodes-base.thehive5trigger.md#_snippet_1

LANGUAGE: sh
CODE:
```
curl -XPUT -uTHEHIVE_USERNAME:THEHIVE_PASSWORD -H 'Content-type: application/json' THEHIVE_URL/api/config/organisation/notification -d '
{
	"value": [
		{
		"delegate": false,
		"trigger": { "name": "AnyEvent"},
		"notifier": { "name": "webhook", "endpoint": "TESTING_WEBHOOK_NAME" }
		},
		{
		"delegate": false,
		"trigger": { "name": "AnyEvent"},
		"notifier": { "name": "webhook", "endpoint": "PRODUCTION_WEBHOOK_NAME" }
		}
	]
}'
```

----------------------------------------

TITLE: n8n Workflow/Template Item Data Schema
DESCRIPTION: This JSON snippet defines the required properties for a workflow or template item within the n8n system. It specifies essential fields such as 'id', 'name', 'totalViews', 'price', 'purchaseUrl', 'recentViews', 'createdAt', 'user', and 'nodes'. It also includes a nested 'required' array, likely for properties of an item within an array (e.g., a node's properties).
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_snippets/workflows/templates/custom-templates-library.md#_snippet_1

LANGUAGE: JSON
CODE:
```
        "required": [
          "id",
          "icon",
          "name",
          "codex",
          "group",
          "defaults",
          "iconData",
          "displayName",
          "typeVersion"
        ]
      }
    }
  },
  "required": [
    "id",
    "name",
    "totalViews",
    "price",
    "purchaseUrl",
    "recentViews",
    "createdAt",
    "user",
    "nodes"
  ]
}
```

----------------------------------------

TITLE: Setting n8n Multi-Main Leader Check Interval via Environment Variable (Shell)
DESCRIPTION: Defines the interval (in seconds) at which main processes check for the leader key in an n8n multi-main setup using an environment variable. This impacts how quickly a new leader is elected if the current one fails.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/queue-mode.md#_snippet_11

LANGUAGE: Shell
CODE:
```
N8N_MULTI_MAIN_SETUP_CHECK_INTERVAL=3
```

----------------------------------------

TITLE: Deactivating an n8n Workflow by ID
DESCRIPTION: This command sets the active status of a specific n8n workflow to 'false' using its unique ID. For the change to take effect, n8n must be restarted after executing this command, as it operates directly on the n8n database.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_2

LANGUAGE: bash
CODE:
```
n8n update:workflow --id=<ID> --active=false
```

----------------------------------------

TITLE: Getting Current Execution ID in Python
DESCRIPTION: This method retrieves the unique identifier for the current workflow execution. It is useful for tracking specific runs of a workflow.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/n8n-metadata.md#_snippet_21

LANGUAGE: Python
CODE:
```
_execution.id
```

----------------------------------------

TITLE: Configuring Convert to File Node in n8n (JSON)
DESCRIPTION: This snippet configures a Convert to File node in n8n to convert incoming data to JSON files. Each file is named dynamically using the 'orderID' from the 'If' node's output.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-5/chapter-5.2.md#_snippet_5

LANGUAGE: JSON
CODE:
```
{
    "parameters": {
        "operation": "toJson",
        "mode": "each",
        "options": {
        "fileName": "=report_orderID_{{ $('If').item.json.orderID }}.json"
        }
    },
    "id": "d93b4429-2200-4a84-8505-16266fedfccd",
    "name": "Convert to File",
    "type": "n8n-nodes-base.convertToFile",
    "typeVersion": 1.1,
    "position": [
        1880,
        500
    ]
}
```

----------------------------------------

TITLE: Disabling Webhook Processing on Main Process (Environment Variable)
DESCRIPTION: This command sets an environment variable to disable production webhook processing on the main n8n instance. This ensures that all webhook executions are handled by dedicated webhook processors, optimizing the main process for UI and API tasks.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/queue-mode.md#_snippet_8

LANGUAGE: bash
CODE:
```
export N8N_DISABLE_PRODUCTION_MAIN_PROCESS=true
```

----------------------------------------

TITLE: Configuring Password String UI Element in n8n (TypeScript)
DESCRIPTION: This snippet shows how to configure a string UI element specifically for password input. It uses `typeOptions: { password: true }` to enable password masking. Other properties like `displayName`, `name`, `type`, `required`, `default`, `description`, and `displayOptions` are also included.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/ui-elements.md#_snippet_1

LANGUAGE: TypeScript
CODE:
```
{
	displayName: 'Password',
	name: 'password',
	type: 'string',
	required: true,
	typeOptions: {
		password: true,
	},
	default: '',
	description: `User's password`,
	displayOptions: { // the resources and operations to display this element with
		show: {
			resource: [
				// comma-separated list of resource names
			],
			operation: [
				// comma-separated list of operation names
			]
		}
	},
}
```

----------------------------------------

TITLE: Supported Scopes for Google Analytics (API Configuration)
DESCRIPTION: These are the specific OAuth2 scopes required for integrating Google Analytics with n8n. They provide access to either full management or read-only access to Analytics data, depending on the scope chosen.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/credentials/google/oauth-generic.md#_snippet_3

LANGUAGE: API Configuration
CODE:
```
https://www.googleapis.com/auth/analytics
```

LANGUAGE: API Configuration
CODE:
```
https://www.googleapis.com/auth/analytics.readonly
```

----------------------------------------

TITLE: Setting Custom Node Paths with N8N_CUSTOM_EXTENSIONS (Bash)
DESCRIPTION: This snippet demonstrates how to set the N8N_CUSTOM_EXTENSIONS environment variable in Bash to specify additional directories where n8n should look for custom nodes. Multiple paths can be provided, separated by semicolons, allowing n8n to load nodes from various locations.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/configuration/configuration-examples/custom-nodes-location.md#_snippet_0

LANGUAGE: bash
CODE:
```
export N8N_CUSTOM_EXTENSIONS="/home/<USER>/n8n/custom-nodes;/data/n8n/nodes"
```

----------------------------------------

TITLE: Triggering Webhook Node: GET Request with Header Parameter using cURL
DESCRIPTION: This cURL command triggers the n8n Webhook node with a GET request that includes a custom header parameter. This is essential for testing scenarios where the webhook expects specific information passed in the request headers.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.webhook/common-issues.md#_snippet_2

LANGUAGE: sh
CODE:
```
curl --request GET <https://your-n8n.url/webhook/path> --header 'key=value'
```

----------------------------------------

TITLE: Defining NASA API Resources in n8n Node (TypeScript)
DESCRIPTION: This snippet defines the available API resources for the n8n node using an 'options' type property. It allows users to select between 'Astronomy Picture of the Day' and 'Mars Rover Photos', mapping them to internal 'value' identifiers. The 'noDataExpression' property prevents data expressions for this field.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/declarative-style-node.md#_snippet_5

LANGUAGE: TypeScript
CODE:
```
properties: [
	{
		displayName: 'Resource',
		name: 'resource',
		type: 'options',
		noDataExpression: true,
		options: [
			{
				name: 'Astronomy Picture of the Day',
				value: 'astronomyPictureOfTheDay',
			},
			{
				name: 'Mars Rover Photos',
				value: 'marsRoverPhotos',
			},
		],
		default: 'astronomyPictureOfTheDay',
	},
	// Operations will go here

]
```

----------------------------------------

TITLE: Setting All Custom Execution Data in n8n Code Node
DESCRIPTION: This snippet shows how to replace the entire custom execution data object with a new set of key-value pairs. This method is useful for bulk updates or initializing the custom data object with multiple properties at once.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/builtin/execution.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
// Set the custom execution data object
$execution.customData.setAll({"key1": "value1", "key2": "value2"})
```

LANGUAGE: Python
CODE:
```
# Set the custom execution data object
_execution.customData.setAll({"key1": "value1", "key2": "value2"})
```

----------------------------------------

TITLE: Scheduling Hourly with n8n Schedule Trigger (Cron)
DESCRIPTION: This Cron expression schedules a workflow to run at the beginning of every hour. The `0` in the minute field ensures that the execution occurs precisely on the hour.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger/index.md#_snippet_2

LANGUAGE: Cron
CODE:
```
0 * * * *
```

----------------------------------------

TITLE: Adding Optional Fields (First/Last Name) to n8n SendGrid Node (TypeScript)
DESCRIPTION: This snippet adds an 'Additional Fields' collection to an n8n node, allowing users to input optional contact details like first name and last name. These fields are displayed under a collapsible section in the UI and are only visible when the 'contact' resource and 'create' operation are selected.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/programmatic-style-node.md#_snippet_7

LANGUAGE: TypeScript
CODE:
```
{
	displayName: 'Additional Fields',
	name: 'additionalFields',
	type: 'collection',
	placeholder: 'Add Field',
	default: {},
	displayOptions: {
		show: {
			resource: [
				'contact',
			],
			operation: [
				'create',
			],
		},
	},
	options: [
		{
			displayName: 'First Name',
			name: 'firstName',
			type: 'string',
			default: '',
		},
		{
			displayName: 'Last Name',
			name: 'lastName',
			type: 'string',
			default: '',
		},
	],
},
```

----------------------------------------

TITLE: Deactivating All n8n Workflows
DESCRIPTION: This command sets the active status of all n8n workflows to 'false'. A restart of the n8n instance is required for these changes to be applied, as the command modifies the n8n database directly.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_4

LANGUAGE: bash
CODE:
```
n8n update:workflow --all --active=false
```

----------------------------------------

TITLE: Activating All n8n Workflows
DESCRIPTION: This command sets the active status of all n8n workflows to 'true'. A restart of the n8n instance is required for these changes to be applied, as the command modifies the n8n database directly.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_5

LANGUAGE: bash
CODE:
```
n8n update:workflow --all --active=true
```

----------------------------------------

TITLE: Configure n8n Binary Data Storage Modes
DESCRIPTION: Set these environment variables to define which binary data storage modes are available to n8n (e.g., filesystem, s3) and which mode should be used by default for new binary data.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/external-storage.md#_snippet_2

LANGUAGE: sh
CODE:
```
export N8N_AVAILABLE_BINARY_DATA_MODES=filesystem,s3
export N8N_DEFAULT_BINARY_DATA_MODE=s3
```

----------------------------------------

TITLE: Identifying Credential Type Name from Workflow JSON (JSON)
DESCRIPTION: This JSON snippet demonstrates how to locate the 'credentialTypeName' within a downloaded n8n workflow JSON. The 'credentialTypeName' (e.g., 'googleDriveOAuth2Api') is found as a key within the 'credentials' object, which is essential for querying specific credential schemas via the API.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/api/using-api-playground.md#_snippet_2

LANGUAGE: json
CODE:
```
{
    ...,
    "credentials": {
        "googleDriveOAuth2Api": {
        "id": "9",
        "name": "Google Drive"
        }
    }
}
```

----------------------------------------

TITLE: Accessing Environment Variables in Python
DESCRIPTION: This method contains n8n instance configuration environment variables. It provides access to system-level settings for self-hosted n8n instances.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/n8n-metadata.md#_snippet_19

LANGUAGE: Python
CODE:
```
_env
```

----------------------------------------

TITLE: Generating Sample User Data in n8n (JavaScript)
DESCRIPTION: This JavaScript snippet, intended for an n8n Code node, creates an array of JSON objects representing user data. Each object includes 'name' and 'language' fields, serving as the first input stream for demonstrating the Merge node's capabilities. It's used to simulate a dataset of users with their preferred languages.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.merge.md#_snippet_1

LANGUAGE: javascript
CODE:
```
return [
  {
    json: {
      name: 'Stefan',
      language: 'de'
    }
  },
  {
    json: {
      name: 'Jim',
      language: 'en'
    }
  },
  {
    json: {
      name: 'Hans',
      language: 'de'
    }
  }
];
```

----------------------------------------

TITLE: JSON Payload for Activating n8n Workflow
DESCRIPTION: This JSON snippet illustrates the essential part of the payload required to activate an n8n workflow. The `"active":true` property is crucial for changing the workflow's status, and it's typically part of a larger workflow definition JSON.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/embed/managing-workflows.md#_snippet_6

LANGUAGE: JSON
CODE:
```
"active":true,
"settings": {},
"staticData": null,
"tags": []
```

----------------------------------------

TITLE: Cloning n8n Node Starter Repository (Shell)
DESCRIPTION: This command sequence clones the n8n node starter repository from GitHub into a new directory named 'n8n-nodes-friendgrid' and then navigates into that directory. This is the initial step for setting up a new n8n node development project.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/programmatic-style-node.md#_snippet_0

LANGUAGE: shell
CODE:
```
git clone https://github.com/<your-organization>/<your-repo-name>.git n8n-nodes-friendgrid
cd n8n-nodes-friendgrid
```

----------------------------------------

TITLE: Testing Command in New n8n Docker Container (Shell)
DESCRIPTION: This shell command allows you to start a temporary Docker container using the n8n image, specifically to execute a command instead of n8n itself. This is useful for testing if a command is available within the n8n Docker environment when n8n is not currently running, helping diagnose 'command not found' issues.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.executecommand/common-issues.md#_snippet_1

LANGUAGE: sh
CODE:
```
# Start up a new container that runs the command instead of n8n
# Use the same image and tag that you use to run n8n normally
docker run -it --rm --entrypoint /bin/sh docker.n8n.io/n8nio/n8n -c <command_to_run>
```

----------------------------------------

TITLE: Dockerfile for Adding cURL to n8n Docker Image
DESCRIPTION: This Dockerfile extends the official n8n Docker image to include the `curl` package, which is not present in the default Alpine Linux-based image. It switches to root to install `curl` using `apk` and then reverts to the `node` user for security.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.executecommand/index.md#_snippet_2

LANGUAGE: shell
CODE:
```
FROM docker.n8n.io/n8nio/n8n
USER root
RUN apk --update add curl
USER node
```

----------------------------------------

TITLE: Adding Dynamic Hint to n8n Programmatic Node (TypeScript)
DESCRIPTION: This snippet demonstrates how to add a dynamic hint to an n8n programmatic-style node. The hint message is generated based on the node's execution data, specifically the number of input items processed. It advises the user to enable 'Execute once' if the node ran multiple times, and the hint is displayed in the output pane after execution.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/ui-elements.md#_snippet_22

LANGUAGE: TypeScript
CODE:
```
if (operation === 'select' && items.length > 1 && !node.executeOnce) {
    // Expects two parameters: NodeExecutionData and an array of hints
	return new NodeExecutionOutput(
		[returnData],
		[
			{
				message: `This node ran ${items.length} times, once for each input item. To run for the first item only, enable <b>Execute once</b> in the node settings.`,
				location: 'outputPane'
			}
		]
	);
}
return [returnData];
```

----------------------------------------

TITLE: Starting Webhook Processor (Docker)
DESCRIPTION: This Docker command runs an n8n container specifically configured as a webhook processor. It maps port 5679 to 5678 and sets the execution mode to 'queue', essential for scaling webhook request handling.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/scaling/queue-mode.md#_snippet_6

LANGUAGE: docker
CODE:
```
docker run --name n8n-queue -p 5679:5678 -e "EXECUTIONS_MODE=queue" docker.n8n.io/n8nio/n8n webhook
```

----------------------------------------

TITLE: Defining Contact Resource for n8n SendGrid Node (TypeScript)
DESCRIPTION: This snippet defines the 'resource' object for an n8n node, specifying the API resource the node interacts with. It configures a dropdown UI element for users to select 'Contact' as the resource, mapping to SendGrid's `/v3/marketing/contacts` endpoint. It ensures the resource selection is required.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/programmatic-style-node.md#_snippet_5

LANGUAGE: TypeScript
CODE:
```
{
	displayName: 'Resource',
	name: 'resource',
	type: 'options',
	options: [
		{
			name: 'Contact',
			value: 'contact',
		},
	],
	default: 'contact',
	noDataExpression: true,
	required: true,
	description: 'Create a new contact',
},
```

----------------------------------------

TITLE: Defining NASA API Operations and Parameters in n8n Node (TypeScript)
DESCRIPTION: This code defines operations and associated parameters for the selected NASA API resources. It includes 'Get' operations for both APOD and Mars Rover photos, specifying HTTP method and URL. Additionally, it defines input fields for 'Rover name' and 'Date' for Mars Rover photos, using 'routing' to dynamically construct the API request URL and query parameters based on user input.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/declarative-style-node.md#_snippet_6

LANGUAGE: TypeScript
CODE:
```
{
	displayName: 'Operation',
	name: 'operation',
	type: 'options',
	noDataExpression: true,
	displayOptions: {
		show: {
			resource: [
				'astronomyPictureOfTheDay',
			],
		},
	},
	options: [
		{
			name: 'Get',
			value: 'get',
			action: 'Get the APOD',
			description: 'Get the Astronomy Picture of the day',
			routing: {
				request: {
					method: 'GET',
					url: '/planetary/apod',
				},
			},
		},
	],
	default: 'get',
},
{
	displayName: 'Operation',
	name: 'operation',
	type: 'options',
	noDataExpression: true,
	displayOptions: {
		show: {
			resource: [
				'marsRoverPhotos',
			],
		},
	},
	options: [
		{
			name: 'Get',
			value: 'get',
			action: 'Get Mars Rover photos',
			description: 'Get photos from the Mars Rover',
			routing: {
				request: {
					method: 'GET',
				},
			},
		},
	],
	default: 'get',
},
{
	displayName: 'Rover name',
	description: 'Choose which Mars Rover to get a photo from',
	required: true,
	name: 'roverName',
		type: 'options',
	options: [
		{name: 'Curiosity', value: 'curiosity'},
		{name: 'Opportunity', value: 'opportunity'},
		{name: 'Perseverance', value: 'perseverance'},
		{name: 'Spirit', value: 'spirit'},
	],
	routing: {
		request: {
			url: '=/mars-photos/api/v1/rovers/{{$value}}/photos',
		},
	},
	default: 'curiosity',
	displayOptions: {
		show: {
			resource: [
				'marsRoverPhotos',
			],
		},
	},
},
{
	displayName: 'Date',
	description: 'Earth date',
	required: true,
	name: 'marsRoverDate',
	type: 'dateTime',
	default:'',
	displayOptions: {
		show: {
			resource: [
				'marsRoverPhotos',
			],
		},
	},
	routing: {
		request: {
			// You've already set up the URL. qs appends the value of the field as a query string
			qs: {
				earth_date: '={{ new Date($value).toISOString().substr(0,10) }}',
			},
		},
	},
},
// Optional/additional fields will go here
```

----------------------------------------

TITLE: Uninstalling a Community Node via npm (Shell)
DESCRIPTION: Uninstall a previously installed community node package from the n8n environment using npm. Replace n8n-nodes-nodeName with the actual package name.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/community-nodes/installation/manual-install.md#_snippet_4

LANGUAGE: shell
CODE:
```
npm uninstall n8n-nodes-nodeName
```

----------------------------------------

TITLE: Initializing Consent Form State and Display - JavaScript
DESCRIPTION: This snippet initializes the consent form's checkbox states based on previously stored consent settings (`__md_get("__consent")`). If no consent is stored and the page is not browsed locally, it introduces a small delay before making the consent form visible to the user.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_overrides/partials/javascripts/consent.html#_snippet_3

LANGUAGE: JavaScript
CODE:
```
var consent = __md_get("__consent")
if (consent) {
  for (var input of document.forms.consent.elements)
    if (input.name) input.checked = consent[input.name] || false
} else if (location.protocol !== "file:") {
  setTimeout(function() {
    var el = document.querySelector("[data-md-component=consent]")
    el.hidden = false
  }, 250)
}
```

----------------------------------------

TITLE: Setting Google Calendar Event Start Time (n8n Expression)
DESCRIPTION: This n8n expression sets the start time of a Google Calendar event to the current timestamp. It is used as the default value for the 'Start Time' parameter when creating an event, ensuring the event begins immediately upon creation or workflow execution.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.googlecalendar/event-operations.md#_snippet_0

LANGUAGE: n8n
CODE:
```
{{ $now }}
```

----------------------------------------

TITLE: Getting Previous Node Output Index in JavaScript
DESCRIPTION: This method provides the index of the output connector from which the current input was received, particularly useful when the previous node has multiple outputs (e.g., If or Switch nodes). When using a Merge node, it consistently refers to the first input connector.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/n8n-metadata.md#_snippet_10

LANGUAGE: JavaScript
CODE:
```
$prevNode.outputIndex
```

----------------------------------------

TITLE: Configuring a Resource Locator UI Field in n8n (TypeScript)
DESCRIPTION: This TypeScript configuration defines a `resourceLocator` UI field for n8n nodes, enabling users to find external resources via ID, URL, or a searchable list. It includes validation rules for ID and URL inputs, and specifies how to extract values or use them in API calls. The `list` mode requires a `searchListMethod` to populate and handle search functionality.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/ui-elements.md#_snippet_13

LANGUAGE: typescript
CODE:
```
{
	displayName: 'Card',
	name: 'cardID',
	type: 'resourceLocator',
	default: '',
	description: 'Get a card',
	modes: [
		{
			displayName: 'ID',
			name: 'id',
			type: 'string',
			hint: 'Enter an ID',
			validation: [
				{
					type: 'regex',
					properties: {
						regex: '^[0-9]',
						errorMessage: 'The ID must start with a number',
					},
				},
			],
			placeholder: '12example',
			// How to use the ID in API call
			url: '=http://api-base-url.com/?id={{$value}}',
		},
		{
			displayName: 'URL',
			name: 'url',
			type: 'string',
			hint: 'Enter a URL',
			validation: [
				{
					type: 'regex',
					properties: {
						regex: '^http',
						errorMessage: 'Invalid URL',
					},
				},
			],
			placeholder: 'https://example.com/card/12example/',
			// How to get the ID from the URL
			extractValue: {
				type: 'regex',
				regex: 'example.com/card/([0-9]*.*)/',
			},
		},
		{
			displayName: 'List',
			name: 'list',
			type: 'list',
			typeOptions: {
				// You must always provide a search method
				// Write this method within the methods object in your base file
				// The method must populate the list, and handle searching if searchable: true
				searchListMethod: 'searchMethod',
				// If you want users to be able to search the list
				searchable: true,
				// Set to true if you want to force users to search
				// When true, users can't browse the list
				// Or false if users can browse a list
				searchFilterRequired: true,
			},
		},
	],
	displayOptions: {
		// the resources and operations to display this element with
		show: {
			resource: [
				// comma-separated list of resource names
			],
			operation: [
				// comma-separated list of operation names
			],
		},
	},
},

```

----------------------------------------

TITLE: Example Customer Data Input JSON
DESCRIPTION: An example JSON array containing multiple customer records, used as the input data for demonstrating the JSON Output transformation in n8n.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.set.md#_snippet_2

LANGUAGE: JSON
CODE:
```
[
  {
    "id": "23423532",
    "name": "Jay Gatsby",
    "email": "<EMAIL>",
    "notes": "Keeps asking about a green light??",
    "country": "US",
    "created": "1925-04-10"
  },
  {
    "id": "23423533",
    "name": "José Arcadio Buendía",
    "email": "<EMAIL>",
    "notes": "Lots of people named after him. Very confusing",
    "country": "CO",
    "created": "1967-05-05"
  },
  {
    "id": "23423534",
    "name": "Max Sendak",
    "email": "<EMAIL>",
    "notes": "Keeps rolling his terrible eyes",
    "country": "US",
    "created": "1963-04-09"
  },
  {
    "id": "23423535",
    "name": "Zaphod Beeblebrox",
    "email": "<EMAIL>",
    "notes": "Felt like I was talking to more than one person",
    "country": null,
    "created": "1979-10-12"
  },
  {
    "id": "23423536",
    "name": "Edmund Pevensie",
    "email": "<EMAIL>",
    "notes": "Passionate sailor",
    "country": "UK",
    "created": "1950-10-16"
  }
]
```

----------------------------------------

TITLE: Ignoring a Sub-Directory with Local File Trigger (Shell)
DESCRIPTION: This shell script snippet demonstrates the Anymatch syntax for the 'Ignore' parameter, allowing the Local File Trigger node to exclude an entire sub-directory and all its contents from being watched. It matches any directory with the specified name.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.localfiletrigger.md#_snippet_1

LANGUAGE: sh
CODE:
```
**/<directoryName>/**
# For example, **/myDirectory/**
```

----------------------------------------

TITLE: Specifying Column Data Types in CrateDB Node
DESCRIPTION: This snippet demonstrates how to specify data types for columns when configuring the CrateDB node in n8n. By appending `:type` to the column name, users can enforce specific data types like `int` or `text` directly within the 'Columns' field, ensuring data integrity during insertion or update operations.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.cratedb.md#_snippet_0

LANGUAGE: Configuration
CODE:
```
id:int,name:text
```

----------------------------------------

TITLE: Activating an n8n Workflow by ID
DESCRIPTION: This command sets the active status of a specific n8n workflow to 'true' using its unique ID. For the change to take effect, n8n must be restarted after executing this command, as it operates directly on the n8n database.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/hosting/cli-commands.md#_snippet_3

LANGUAGE: bash
CODE:
```
n8n update:workflow --id=<ID> --active=true
```

----------------------------------------

TITLE: Setting Google Calendar Event End Time (n8n Expression)
DESCRIPTION: This n8n expression sets the end time of a Google Calendar event to one hour from the current timestamp. It serves as the default value for the 'End Time' parameter when creating an event, providing a common duration for newly created events.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/app-nodes/n8n-nodes-base.googlecalendar/event-operations.md#_snippet_1

LANGUAGE: n8n
CODE:
```
{{ $now.plus(1, 'hour') }}
```

----------------------------------------

TITLE: Defining a Fixed Collection UI Field in n8n (TypeScript)
DESCRIPTION: This TypeScript configuration defines a `fixedCollection` UI field named 'Metadata' for n8n nodes. It allows grouping semantically related fields, such as 'Name' and 'Value' for metadata keys, and supports multiple values. The `displayOptions` control its visibility based on resource and operation.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/creating-nodes/build/reference/ui-elements.md#_snippet_12

LANGUAGE: typescript
CODE:
```
{
	displayName: 'Metadata',
	name: 'metadataUi',
	placeholder: 'Add Metadata',
	type: 'fixedCollection',
	default: '',
	typeOptions: {
		multipleValues: true,
	},
	description: '',
	options: [
		{
			name: 'metadataValues',
			displayName: 'Metadata',
			values: [
				{
					displayName: 'Name',
					name: 'name',
					type: 'string',
					default: 'Name of the metadata key to add.',
				},
				{
					displayName: 'Value',
					name: 'value',
					type: 'string',
					default: '',
					description: 'Value to set for the metadata key.',
				},
			],
		},
	],
	displayOptions: { // the resources and operations to display this element with
		show: {
			resource: [
				// comma-separated list of resource names
			],
			operation: [
				// comma-separated list of operation names
			]
		}
	},
}
```

----------------------------------------

TITLE: Default Error Data Structure for n8n Error Trigger (JSON)
DESCRIPTION: This JSON object represents the default data structure received by the n8n Error Trigger when an error occurs during workflow execution. It includes details about the execution, the error message, stack trace, and the workflow itself. Specific fields like `execution.id`, `execution.url`, and `execution.retryOf` are conditionally present based on execution context.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_snippets/integrations/builtin/core-nodes/error-trigger/error-data.md#_snippet_0

LANGUAGE: json
CODE:
```
[
	{
		"execution": {
			"id": "231",
			"url": "https://n8n.example.com/execution/231",
			"retryOf": "34",
			"error": {
				"message": "Example Error Message",
				"stack": "Stacktrace"
			},
			"lastNodeExecuted": "Node With Error",
			"mode": "manual"
		},
		"workflow": {
			"id": "1",
			"name": "Example Workflow"
		}
	}
]
```

----------------------------------------

TITLE: Implementing Client-Side URL Redirects in JavaScript
DESCRIPTION: This JavaScript snippet defines a self-executing function that checks the current browser path against a `redirects` object. If a matching redirect URL is found, the browser is navigated to the new URL. This is useful for handling old or moved page links directly in the client, providing a smooth user experience for broken links.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/_overrides/404.html#_snippet_0

LANGUAGE: JavaScript
CODE:
```
// Create an object containing all your redirects
const redirects = {
// Use \_redirects file to set redirects
};
(function redirect(){
const currentPath = window.location.pathname + (window.location.hash ? window.location.hash : '');
const redirectUrl = redirects\[currentPath\];
if (redirectUrl) {
window.location.href = `${window.location.origin}${redirectUrl}`;
return;
}
})();
```

----------------------------------------

TITLE: Resulting Output JSON from n8n Transformation
DESCRIPTION: The final JSON output generated by the n8n node after applying the specified JSON Output configuration to the input data. It shows the original input fields combined with the new keys, arrays, and objects created using expressions.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/core-nodes/n8n-nodes-base.set.md#_snippet_4

LANGUAGE: JSON
CODE:
```
[
  {
    "id": "23423532",
    "name": "Jay Gatsby",
    "email": "<EMAIL>",
    "notes": "Keeps asking about a green light??",
    "country": "US",
    "created": "1925-04-10",
    "newKey": "new value",
    "array": [
      23423532,
      "Jay Gatsby"
    ],
    "object": {
      "innerKey1": "new value",
      "innerKey2": "23423532",
      "innerKey3": "Jay Gatsby"
    }
  },
  {
    "id": "23423533",
    "name": "José Arcadio Buendía",
    "email": "<EMAIL>",
    "notes": "Lots of people named after him. Very confusing",
    "country": "CO",
    "created": "1967-05-05",
    "newKey": "new value",
    "array": [
      23423533,
      "José Arcadio Buendía"
    ],
    "object": {
      "innerKey1": "new value",
      "innerKey2": "23423533",
      "innerKey3": "José Arcadio Buendía"
    }
  },
  {
    "id": "23423534",
    "name": "Max Sendak",
    "email": "<EMAIL>",
    "notes": "Keeps rolling his terrible eyes",
    "country": "US",
    "created": "1963-04-09",
    "newKey": "new value",
    "array": [
      23423534,
      "Max Sendak"
    ],
    "object": {
      "innerKey1": "new value",
      "innerKey2": "23423534",
      "innerKey3": "Max Sendak"
    }
  },
  {
    "id": "23423535",
    "name": "Zaphod Beeblebrox",
    "email": "<EMAIL>",
    "notes": "Felt like I was talking to more than one person",
    "country": null,
    "created": "1979-10-12",
    "newKey": "new value",
    "array": [
      23423535,
      "Zaphod Beeblebrox"
    ],
    "object": {
      "innerKey1": "new value",
      "innerKey2": "23423535",
      "innerKey3": "Zaphod Beeblebrox"
    }
  },
  {
    "id": "23423536",
    "name": "Edmund Pevensie",
    "email": "<EMAIL>",
    "notes": "Passionate sailor",
    "country": "UK",
    "created": "1950-10-16",
    "newKey": "new value",
    "array": [
      23423536,
      "Edmund Pevensie"
    ],
    "object": {
      "innerKey1": "new value",
      "innerKey2": "23423536",
      "innerKey3": "Edmund Pevensie"
    }
  }
]
```

----------------------------------------

TITLE: Setting All Custom Execution Data (Code Node)
DESCRIPTION: This snippet shows how to overwrite all existing custom execution data with a new object using the Code node in an n8n workflow. This allows for bulk updates of execution metadata.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/workflows/executions/custom-executions-data.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
$execution.customData.setAll({"key1": "value1", "key2": "value2"})
```

LANGUAGE: Python
CODE:
```
_execution.customData.setAll({"key1": "value1", "key2": "value2"})
```

----------------------------------------

TITLE: Configuring Airtable Node in n8n (JSON)
DESCRIPTION: This snippet configures an Airtable node in an n8n workflow. It specifies the node's name, type, version, position, and references a personal access token credential for authentication.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/courses/level-two/chapter-5/chapter-5.2.md#_snippet_1

LANGUAGE: JSON
CODE:
```
{
    "name": "Airtable",
    "type": "n8n-nodes-base.airtable",
    "typeVersion": 2,
    "position": [
        1000,
        700
    ],
    "credentials": {
        "airtableTokenApi": {
        "id": "MIplo6lY3AEsdf7L",
        "name": "Airtable Personal Access Token account 4"
        }
    }
}
```

----------------------------------------

TITLE: Counting Previous Node Items in JavaScript
DESCRIPTION: This JavaScript snippet is used within an n8n workflow to count the number of items returned by the previous node. It checks if the first item's JSON object is empty; if so, it returns 0, otherwise, it returns the total count of items. The output is formatted as a JSON object with a 'results' key.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/cookbook/code-node/number-items-last-node.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
if (Object.keys(items[0].json).length === 0) {
	return [
		{
			json: {
				results: 0,
			}
		}
	]
}
return [
	{
		json: {
			results: items.length,
		}
	}
];
```

LANGUAGE: JSON
CODE:
```
[
	{
		"results": 8
	}
]
```

----------------------------------------

TITLE: Running n8n in Docker on Linux with Host Network Access (Shell)
DESCRIPTION: This command runs the n8n Docker container, configuring it to map `host.docker.internal` to the host's gateway, which is necessary for n8n to connect to a locally hosted Ollama instance when n8n is containerized on Linux. It also publishes port 5678 and mounts a volume for n8n data.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmollama/common-issues.md#_snippet_1

LANGUAGE: Shell
CODE:
```
docker run -it --rm --add-host host.docker.internal:host-gateway --name n8n -p 5678:5678 -v n8n_data:/home/<USER>/.n8n docker.n8n.io/n8nio/n8n
```

----------------------------------------

TITLE: Accessing External Secrets Information in JavaScript
DESCRIPTION: This method provides access to information regarding your External secrets setup within n8n. It allows interaction with configured secret management.
SOURCE: https://github.com/n8n-io/n8n-docs/blob/main/docs/code/builtin/n8n-metadata.md#_snippet_13

LANGUAGE: JavaScript
CODE:
```
$secrets
```