# Quick Setup Guide - YouTube Knowledge Graph Multi-Agent System

## 🚀 Quick Start (5 Minutes)

### Step 1: Start Memgraph Database
```bash
# Run Memgraph in Docker (required)
docker run -p 7687:7687 -p 7444:7444 memgraph/memgraph-mage --schema-info-enabled=True
```

### Step 2: Import Workflow
1. Open your n8n instance
2. Go to **Workflows** → **Import from File**
3. Select `youtube-knowledge-graph-workflow.json`
4. Click **Import**

### Step 3: Configure API Key
1. Open the imported workflow
2. Click on the **"Set API Key"** node
3. Replace `YOUR_GOOGLE_API_KEY_HERE` with your actual Google AI Studio API key
4. **Save** the workflow

### Step 4: Test the Workflow
1. Use the **Chat Trigger**: Send a message like:
   ```
   Analyze this video: https://www.youtube.com/watch?v=dQw4w9WgXcQ
   ```
2. Or use the **Workflow Trigger** from another workflow
3. Monitor the execution progress

## 🔑 Getting Your Google API Key

1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Sign in with your Google account
3. Click **"Get API Key"**
4. Create a new API key
5. Copy the key and paste it into the "Set API Key" node

## 🗄️ Memgraph Collection Setup

The workflow automatically uses the **"Ai-personas"** collection. If you need to create it manually:

1. Open Memgraph Lab at `http://localhost:3000`
2. Run this query to verify the collection:
   ```cypher
   MATCH (n:AiPersonas) RETURN count(n);
   ```

## 📊 Workflow Structure

```
Chat/Workflow Trigger → Set API Key → Process URLs → Split Videos → 
Analyze Video → Split Personas → Extract Knowledge → Store in Memgraph → Test
```

## 🧪 Testing Your Setup

The workflow includes built-in tests. After execution, check the **Comprehensive Test Suite** output for:

- ✅ API connectivity
- ✅ Database connectivity  
- ✅ Data integrity
- ✅ Knowledge graph structure

## 🔧 Troubleshooting

### Memgraph Not Starting
```bash
# Check if Docker is running
docker ps

# Stop any existing Memgraph containers
docker stop $(docker ps -q --filter ancestor=memgraph/memgraph-mage)

# Restart Memgraph
docker run -p 7687:7687 -p 7444:7444 memgraph/memgraph-mage --schema-info-enabled=True
```

### API Key Issues
- Ensure the key is not still `YOUR_GOOGLE_API_KEY_HERE`
- Verify the key has access to Gemini 2.5 Flash
- Check for any API usage limits

### Workflow Execution Issues
- Check that all nodes are connected properly
- Verify the Split In Batches nodes are configured correctly
- Monitor the execution log for specific error messages

## 📝 Usage Examples

### Chat Trigger Examples
```
"Analyze these YouTube videos: [URL1] [URL2]"
"Process this video: https://www.youtube.com/watch?v=example"
"Extract knowledge from: [multiple URLs separated by spaces]"
```

### Workflow Trigger Data Format
```json
{
  "youtubeUrls": [
    "https://www.youtube.com/watch?v=example1",
    "https://www.youtube.com/watch?v=example2"
  ]
}
```

## 🎯 Expected Results

After successful execution, you should see:

1. **Video Analysis**: Personas identified in each video
2. **Knowledge Extraction**: Detailed knowledge graphs for each persona
3. **Database Storage**: All data stored in Memgraph Ai-personas collection
4. **Test Results**: Comprehensive validation report

## 📈 Monitoring Progress

Watch the workflow execution in n8n:

1. **Input Processor**: URLs validated and prepared
2. **Split Videos**: Processing one video at a time
3. **Video Analysis**: Gemini analyzing each video
4. **Split Personas**: Processing one persona at a time
5. **Knowledge Extraction**: Extracting detailed knowledge
6. **Memgraph Storage**: Storing in Ai-personas collection
7. **Test Suite**: Final validation

## 🔍 Querying Your Data

Once data is stored, query it in Memgraph Lab:

```cypher
// Find all personas
MATCH (p:Person:AiPersonas) RETURN p.name, p.role LIMIT 10;

// Find expertise relationships
MATCH (p:Person:AiPersonas)-[r:HAS_EXPERTISE]->(k:Knowledge:AiPersonas) 
RETURN p.name, k.domain, r.level;

// Find video appearances
MATCH (p:Person:AiPersonas)-[:APPEARS_IN]->(v:Video:AiPersonas) 
RETURN p.name, v.url;
```

## 🆘 Need Help?

1. Check the **Comprehensive Test Suite** output for specific errors
2. Review the n8n execution log for detailed error messages
3. Verify all prerequisites are met (Docker, API key, n8n)
4. Ensure YouTube URLs are public and accessible

## ✅ Success Indicators

You'll know everything is working when:

- ✅ Memgraph container is running on ports 7687 and 7444
- ✅ API key is properly configured in the workflow
- ✅ YouTube URLs are being processed successfully
- ✅ Personas are being identified and stored
- ✅ Knowledge graphs are being created in the Ai-personas collection
- ✅ Test suite shows all green checkmarks

Happy knowledge graph building! 🎉
