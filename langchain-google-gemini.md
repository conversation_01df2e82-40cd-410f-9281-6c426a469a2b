Google
All functionality related to Google Cloud, Google Gemini and other Google products.

Google Generative AI (Gemini API & AI Studio): Access Google Gemini models directly via the Gemini API. Use Google AI Studio for rapid prototyping and get started quickly with the langchain-google-genai package. This is often the best starting point for individual developers.
Google Cloud (Vertex AI & other services): Access Gemini models, Vertex AI Model Garden and a wide range of cloud services (databases, storage, document AI, etc.) via the Google Cloud Platform. Use the langchain-google-vertexai package for Vertex AI models and specific packages (e.g., langchain-google-cloud-sql-pg, langchain-google-community) for other cloud services. This is ideal for developers already using Google Cloud or needing enterprise features like MLOps, specific model tuning or enterprise support.
See Google's guide on migrating from the Gemini API to Vertex AI for more details on the differences.

Integration packages for Gemini models and the Vertex AI platform are maintained in the langchain-google repository. You can find a host of LangChain integrations with other Google APIs and services in the googleapis Github organization and the langchain-google-community package.

Google Generative AI (Gemini API & AI Studio)
Access Google Gemini models directly using the Gemini API, best suited for rapid development and experimentation. Gemini models are available in Google AI Studio.

pip install -U langchain-google-genai

Start for free and get your API key from Google AI Studio.

export GOOGLE_API_KEY="YOUR_API_KEY"

Chat Models
Use the ChatGoogleGenerativeAI class to interact with Gemini 2.0 and 2.5 models. See details in this guide.

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage

llm = ChatGoogleGenerativeAI(model="gemini-2.0-flash")

# Simple text invocation
result = llm.invoke("Sing a ballad of LangChain.")
print(result.content)

# Multimodal invocation with gemini-pro-vision
message = HumanMessage(
    content=[
        {
            "type": "text",
            "text": "What's in this image?",
        },
        {"type": "image_url", "image_url": "https://picsum.photos/seed/picsum/200/300"},
    ]
)
result = llm.invoke([message])
print(result.content)

API Reference:ChatGoogleGenerativeAI | HumanMessage
The image_url can be a public URL, a GCS URI (gs://...), a local file path, a base64 encoded image string (data:image/png;base64,...), or a PIL Image object.

Embedding Models
Generate text embeddings using models like gemini-embedding-exp-03-07 with the GoogleGenerativeAIEmbeddings class.

See a usage example.

from langchain_google_genai import GoogleGenerativeAIEmbeddings

embeddings = GoogleGenerativeAIEmbeddings(model="models/gemini-embedding-exp-03-07")
vector = embeddings.embed_query("What are embeddings?")
print(vector[:5])

API Reference:GoogleGenerativeAIEmbeddings
LLMs
Access the same Gemini models using the (legacy) LLM interface with the GoogleGenerativeAI class.

See a usage example.

from langchain_google_genai import GoogleGenerativeAI

llm = GoogleGenerativeAI(model="gemini-2.0-flash")
result = llm.invoke("Sing a ballad of LangChain.")
print(result)

API Reference:GoogleGenerativeAI
Google Cloud
Access Gemini models, Vertex AI Model Garden and other Google Cloud services via Vertex AI and specific cloud integrations.

Vertex AI models require the langchain-google-vertexai package. Other services might require additional packages like langchain-google-community, langchain-google-cloud-sql-pg, etc.

pip install langchain-google-vertexai
# pip install langchain-google-community[...] # For other services

Google Cloud integrations typically use Application Default Credentials (ADC). Refer to the Google Cloud authentication documentation for setup instructions (e.g., using gcloud auth application-default login).

Chat Models
Vertex AI
Access chat models like Gemini via the Vertex AI platform.

See a usage example.

from langchain_google_vertexai import ChatVertexAI

API Reference:ChatVertexAI
Anthropic on Vertex AI Model Garden
See a usage example.

from langchain_google_vertexai.model_garden import ChatAnthropicVertex

API Reference:ChatAnthropicVertex
Llama on Vertex AI Model Garden
from langchain_google_vertexai.model_garden_maas.llama import VertexModelGardenLlama

API Reference:VertexModelGardenLlama
Mistral on Vertex AI Model Garden
from langchain_google_vertexai.model_garden_maas.mistral import VertexModelGardenMistral

API Reference:VertexModelGardenMistral
Gemma local from Hugging Face
Local Gemma model loaded from HuggingFace. Requires langchain-google-vertexai.

from langchain_google_vertexai.gemma import GemmaChatLocalHF

API Reference:GemmaChatLocalHF
Gemma local from Kaggle
Local Gemma model loaded from Kaggle. Requires langchain-google-vertexai.

from langchain_google_vertexai.gemma import GemmaChatLocalKaggle

API Reference:GemmaChatLocalKaggle
Gemma on Vertex AI Model Garden
Requires langchain-google-vertexai.

from langchain_google_vertexai.gemma import GemmaChatVertexAIModelGarden

API Reference:GemmaChatVertexAIModelGarden
Vertex AI image captioning
Implementation of the Image Captioning model as a chat. Requires langchain-google-vertexai.

from langchain_google_vertexai.vision_models import VertexAIImageCaptioningChat

API Reference:VertexAIImageCaptioningChat
Vertex AI image editor
Given an image and a prompt, edit the image. Currently only supports mask-free editing. Requires langchain-google-vertexai.

from langchain_google_vertexai.vision_models import VertexAIImageEditorChat

API Reference:VertexAIImageEditorChat
Vertex AI image generator
Generates an image from a prompt. Requires langchain-google-vertexai.

from langchain_google_vertexai.vision_models import VertexAIImageGeneratorChat

API Reference:VertexAIImageGeneratorChat
Vertex AI visual QnA
Chat implementation of a visual QnA model. Requires langchain-google-vertexai.

from langchain_google_vertexai.vision_models import VertexAIVisualQnAChat

API Reference:VertexAIVisualQnAChat
LLMs
You can also use the (legacy) string-in, string-out LLM interface.

Vertex AI Model Garden
Access Gemini, and hundreds of OSS models via Vertex AI Model Garden service. Requires langchain-google-vertexai.

See a usage example.

from langchain_google_vertexai import VertexAIModelGarden

API Reference:VertexAIModelGarden
Gemma local from Hugging Face
Local Gemma model loaded from HuggingFace. Requires langchain-google-vertexai.

from langchain_google_vertexai.gemma import GemmaLocalHF

API Reference:GemmaLocalHF
Gemma local from Kaggle
Local Gemma model loaded from Kaggle. Requires langchain-google-vertexai.

from langchain_google_vertexai.gemma import GemmaLocalKaggle

API Reference:GemmaLocalKaggle
Gemma on Vertex AI Model Garden
Requires langchain-google-vertexai.

from langchain_google_vertexai.gemma import GemmaVertexAIModelGarden

API Reference:GemmaVertexAIModelGarden
Vertex AI image captioning
Implementation of the Image Captioning model as an LLM. Requires langchain-google-vertexai.

from langchain_google_vertexai.vision_models import VertexAIImageCaptioning

API Reference:VertexAIImageCaptioning
Embedding Models
Vertex AI
Generate embeddings using models deployed on Vertex AI. Requires langchain-google-vertexai.

See a usage example.

from langchain_google_vertexai import VertexAIEmbeddings

API Reference:VertexAIEmbeddings
Document Loaders
Load documents from various Google Cloud sources.

AlloyDB for PostgreSQL
Google Cloud AlloyDB is a fully managed PostgreSQL-compatible database service.

Install the python package:

pip install langchain-google-alloydb-pg

See usage example.

from langchain_google_alloydb_pg import AlloyDBLoader # AlloyDBEngine also available

BigQuery
Google Cloud BigQuery is a serverless data warehouse.

Install with BigQuery dependencies:

pip install langchain-google-community[bigquery]

See a usage example.

from langchain_google_community import BigQueryLoader

API Reference:BigQueryLoader
Bigtable
Google Cloud Bigtable is a fully managed NoSQL Big Data database service.

Install the python package:

pip install langchain-google-bigtable

See usage example.

from langchain_google_bigtable import BigtableLoader

Cloud SQL for MySQL
Google Cloud SQL for MySQL is a fully-managed MySQL database service.

Install the python package:

pip install langchain-google-cloud-sql-mysql

See usage example.

from langchain_google_cloud_sql_mysql import MySQLLoader # MySQLEngine also available

Cloud SQL for SQL Server
Google Cloud SQL for SQL Server is a fully-managed SQL Server database service.

Install the python package:

pip install langchain-google-cloud-sql-mssql

See usage example.

from langchain_google_cloud_sql_mssql import MSSQLLoader # MSSQLEngine also available

Cloud SQL for PostgreSQL
Google Cloud SQL for PostgreSQL is a fully-managed PostgreSQL database service.

Install the python package:

pip install langchain-google-cloud-sql-pg

See usage example.

from langchain_google_cloud_sql_pg import PostgresLoader # PostgresEngine also available

Cloud Storage
Cloud Storage is a managed service for storing unstructured data.

Install with GCS dependencies:

pip install langchain-google-community[gcs]

Load from a directory or a specific file:

See directory usage example.

from langchain_google_community import GCSDirectoryLoader

API Reference:GCSDirectoryLoader
See file usage example.

from langchain_google_community import GCSFileLoader

API Reference:GCSFileLoader
Cloud Vision loader
Load data using Google Cloud Vision API.

Install with Vision dependencies:

pip install langchain-google-community[vision]

from langchain_google_community.vision import CloudVisionLoader

API Reference:CloudVisionLoader
El Carro for Oracle Workloads
Google El Carro Oracle Operator runs Oracle databases in Kubernetes.

Install the python package:

pip install langchain-google-el-carro

See usage example.

from langchain_google_el_carro import ElCarroLoader

Firestore (Native Mode)
Google Cloud Firestore is a NoSQL document database.

Install the python package:

pip install langchain-google-firestore

See usage example.

from langchain_google_firestore import FirestoreLoader

Firestore (Datastore Mode)
Google Cloud Firestore in Datastore mode.

Install the python package:

pip install langchain-google-datastore

See usage example.

from langchain_google_datastore import DatastoreLoader

Memorystore for Redis
Google Cloud Memorystore for Redis is a fully managed Redis service.

Install the python package:

pip install langchain-google-memorystore-redis

See usage example.

from langchain_google_memorystore_redis import MemorystoreDocumentLoader

Spanner
Google Cloud Spanner is a fully managed, globally distributed relational database service.

Install the python package:

pip install langchain-google-spanner

See usage example.

from langchain_google_spanner import SpannerLoader

Speech-to-Text
Google Cloud Speech-to-Text transcribes audio files.

Install with Speech-to-Text dependencies:

pip install langchain-google-community[speech]

See usage example and authorization instructions.

from langchain_google_community import SpeechToTextLoader

API Reference:SpeechToTextLoader
Document Transformers
Transform documents using Google Cloud services.

Document AI
Google Cloud Document AI is a Google Cloud service that transforms unstructured data from documents into structured data, making it easier to understand, analyze, and consume.

We need to set up a GCS bucket and create your own OCR processor The GCS_OUTPUT_PATH should be a path to a folder on GCS (starting with gs://) and a processor name should look like projects/PROJECT_NUMBER/locations/LOCATION/processors/PROCESSOR_ID. We can get it either programmatically or copy from the Prediction endpoint section of the Processor details tab in the Google Cloud Console.

pip install langchain-google-community[docai]

See a usage example.

from langchain_core.document_loaders.blob_loaders import Blob
from langchain_google_community import DocAIParser

API Reference:Blob | DocAIParser
Google Translate
Google Translate is a multilingual neural machine translation service developed by Google to translate text, documents and websites from one language into another.

The GoogleTranslateTransformer allows you to translate text and HTML with the Google Cloud Translation API.

First, we need to install the langchain-google-community with translate dependencies.

pip install langchain-google-community[translate]

See usage example and authorization instructions.

from langchain_google_community import GoogleTranslateTransformer

API Reference:GoogleTranslateTransformer
Vector Stores
Store and search vectors using Google Cloud databases and Vertex AI Vector Search.

AlloyDB for PostgreSQL
Google Cloud AlloyDB is a fully managed relational database service that offers high performance, seamless integration, and impressive scalability on Google Cloud. AlloyDB is 100% compatible with PostgreSQL.

Install the python package:

pip install langchain-google-alloydb-pg

See usage example.

from langchain_google_alloydb_pg import AlloyDBVectorStore # AlloyDBEngine also available

BigQuery Vector Search
Google Cloud BigQuery, BigQuery is a serverless and cost-effective enterprise data warehouse in Google Cloud.

Google Cloud BigQuery Vector Search BigQuery vector search lets you use GoogleSQL to do semantic search, using vector indexes for fast but approximate results, or using brute force for exact results.

It can calculate Euclidean or Cosine distance. With LangChain, we default to use Euclidean distance.

We need to install several python packages.

pip install google-cloud-bigquery

See usage example.

# Note: BigQueryVectorSearch might be in langchain or langchain_community depending on version
# Check imports in the usage example.
from langchain.vectorstores import BigQueryVectorSearch # Or langchain_community.vectorstores

Memorystore for Redis
Vector store using Memorystore for Redis.

Install the python package:

pip install langchain-google-memorystore-redis

See usage example.

from langchain_google_memorystore_redis import RedisVectorStore

Spanner
Vector store using Cloud Spanner.

Install the python package:

pip install langchain-google-spanner

See usage example.

from langchain_google_spanner import SpannerVectorStore

Firestore (Native Mode)
Vector store using Firestore.

Install the python package:

pip install langchain-google-firestore

See usage example.

from langchain_google_firestore import FirestoreVectorStore

Cloud SQL for MySQL
Vector store using Cloud SQL for MySQL.

Install the python package:

pip install langchain-google-cloud-sql-mysql

See usage example.

from langchain_google_cloud_sql_mysql import MySQLVectorStore # MySQLEngine also available

Cloud SQL for PostgreSQL
Vector store using Cloud SQL for PostgreSQL.

Install the python package:

pip install langchain-google-cloud-sql-pg

See usage example.

from langchain_google_cloud_sql_pg import PostgresVectorStore # PostgresEngine also available

Vertex AI Vector Search
Google Cloud Vertex AI Vector Search from Google Cloud, formerly known as Vertex AI Matching Engine, provides the industry's leading high-scale low latency vector database. These vector databases are commonly referred to as vector similarity-matching or an approximate nearest neighbor (ANN) service.

Install the python package:

pip install langchain-google-vertexai

See a usage example.

from langchain_google_vertexai import VectorSearchVectorStore

API Reference:VectorSearchVectorStore
With DataStore Backend
Vector search using Datastore for document storage.

See usage example.

from langchain_google_vertexai import VectorSearchVectorStoreDatastore

API Reference:VectorSearchVectorStoreDatastore
With GCS Backend
Alias for VectorSearchVectorStore storing documents/index in GCS.

from langchain_google_vertexai import VectorSearchVectorStoreGCS

API Reference:VectorSearchVectorStoreGCS
Retrievers
Retrieve information using Google Cloud services.

Vertex AI Search
Build generative AI powered search engines using Vertex AI Search. from Google Cloud allows developers to quickly build generative AI powered search engines for customers and employees.

See a usage example.

Note: GoogleVertexAISearchRetriever is deprecated. Use the components below from langchain-google-community.

Install the google-cloud-discoveryengine package for underlying access.

pip install google-cloud-discoveryengine langchain-google-community

VertexAIMultiTurnSearchRetriever
from langchain_google_community import VertexAIMultiTurnSearchRetriever

API Reference:VertexAIMultiTurnSearchRetriever
VertexAISearchRetriever
# Note: The example code shows VertexAIMultiTurnSearchRetriever, confirm if VertexAISearchRetriever is separate or related.
# Assuming it might be related or a typo in the original doc:
from langchain_google_community import VertexAISearchRetriever # Verify class name if needed


API Reference:VertexAISearchRetriever
VertexAISearchSummaryTool
from langchain_google_community import VertexAISearchSummaryTool

API Reference:VertexAISearchSummaryTool
Document AI Warehouse
Search, store, and manage documents using Document AI Warehouse.

Note: GoogleDocumentAIWarehouseRetriever (from langchain) is deprecated. Use DocumentAIWarehouseRetriever from langchain-google-community.

Requires installation of relevant Document AI packages (check specific docs).

pip install langchain-google-community # Add specific docai dependencies if needed

from langchain_google_community.documentai_warehouse import DocumentAIWarehouseRetriever

API Reference:DocumentAIWarehouseRetriever
Tools
Integrate agents with various Google services.

Text-to-Speech
Google Cloud Text-to-Speech is a Google Cloud service that enables developers to synthesize natural-sounding speech with 100+ voices, available in multiple languages and variants. It applies DeepMind's groundbreaking research in WaveNet and Google's powerful neural networks to deliver the highest fidelity possible.

Install required packages:

pip install google-cloud-text-to-speech langchain-google-community

See usage example and authorization instructions.

from langchain_google_community import TextToSpeechTool

API Reference:TextToSpeechTool
Google Drive
Tools for interacting with Google Drive.

Install required packages:

pip install google-api-python-client google-auth-httplib2 google-auth-oauthlib langchain-googledrive

See usage example and authorization instructions.

from langchain_googledrive.utilities.google_drive import GoogleDriveAPIWrapper
from langchain_googledrive.tools.google_drive.tool import GoogleDriveSearchTool

Google Finance
Query financial data. Requires google-search-results package and SerpApi key.

pip install google-search-results langchain-community # Requires langchain-community

See usage example and authorization instructions.

from langchain_community.tools.google_finance import GoogleFinanceQueryRun
from langchain_community.utilities.google_finance import GoogleFinanceAPIWrapper

API Reference:GoogleFinanceQueryRun | GoogleFinanceAPIWrapper
Google Jobs
Query job listings. Requires google-search-results package and SerpApi key.

pip install google-search-results langchain-community # Requires langchain-community

See usage example and authorization instructions.

from langchain_community.tools.google_jobs import GoogleJobsQueryRun
# Note: Utilities might be shared, e.g., GoogleFinanceAPIWrapper was listed, verify correct utility
# from langchain_community.utilities.google_jobs import GoogleJobsAPIWrapper # If exists

API Reference:GoogleJobsQueryRun | GoogleJobsAPIWrapper
Google Lens
Perform visual searches. Requires google-search-results package and SerpApi key.

pip install google-search-results langchain-community # Requires langchain-community

See usage example and authorization instructions.

from langchain_community.tools.google_lens import GoogleLensQueryRun
from langchain_community.utilities.google_lens import GoogleLensAPIWrapper

API Reference:GoogleLensQueryRun | GoogleLensAPIWrapper
Google Places
Search for places information. Requires googlemaps package and a Google Maps API key.

pip install googlemaps langchain # Requires base langchain

See usage example and authorization instructions.

# Note: GooglePlacesTool might be in langchain or langchain_community depending on version
from langchain.tools import GooglePlacesTool # Or langchain_community.tools

API Reference:GooglePlacesTool
Google Scholar
Search academic papers. Requires google-search-results package and SerpApi key.

pip install google-search-results langchain-community # Requires langchain-community

See usage example and authorization instructions.

from langchain_community.tools.google_scholar import GoogleScholarQueryRun
from langchain_community.utilities.google_scholar import GoogleScholarAPIWrapper

API Reference:GoogleScholarQueryRun | GoogleScholarAPIWrapper
Google Search
Perform web searches using Google Custom Search Engine (CSE). Requires GOOGLE_API_KEY and GOOGLE_CSE_ID.

Install langchain-google-community:

pip install langchain-google-community

Wrapper:

from langchain_google_community import GoogleSearchAPIWrapper

API Reference:GoogleSearchAPIWrapper
Tools:

from langchain_community.tools import GoogleSearchRun, GoogleSearchResults

API Reference:GoogleSearchRun | GoogleSearchResults
Agent Loading:

from langchain_community.agent_toolkits.load_tools import load_tools
tools = load_tools(["google-search"])

API Reference:load_tools
See detailed notebook.

Google Trends
Query Google Trends data. Requires google-search-results package and SerpApi key.

pip install google-search-results langchain-community # Requires langchain-community

See usage example and authorization instructions.

from langchain_community.tools.google_trends import GoogleTrendsQueryRun
from langchain_community.utilities.google_trends import GoogleTrendsAPIWrapper

API Reference:GoogleTrendsQueryRun | GoogleTrendsAPIWrapper
Toolkits
Collections of tools for specific Google services.

GMail
Google Gmail is a free email service provided by Google. This toolkit works with emails through the Gmail API.

pip install langchain-google-community[gmail]

See usage example and authorization instructions.

# Load the whole toolkit
from langchain_google_community import GmailToolkit

# Or use individual tools
from langchain_google_community.gmail.create_draft import GmailCreateDraft
from langchain_google_community.gmail.get_message import GmailGetMessage
from langchain_google_community.gmail.get_thread import GmailGetThread
from langchain_google_community.gmail.search import GmailSearch
from langchain_google_community.gmail.send_message import GmailSendMessage

API Reference:GmailToolkit | GmailCreateDraft | GmailGetMessage | GmailGetThread | GmailSearch | GmailSendMessage
Memory
Store conversation history using Google Cloud databases.

AlloyDB for PostgreSQL
Chat memory using AlloyDB.

Install the python package:

pip install langchain-google-alloydb-pg

See usage example.

from langchain_google_alloydb_pg import AlloyDBChatMessageHistory # AlloyDBEngine also available

Cloud SQL for PostgreSQL
Chat memory using Cloud SQL for PostgreSQL.

Install the python package:

pip install langchain-google-cloud-sql-pg

See usage example.

from langchain_google_cloud_sql_pg import PostgresChatMessageHistory # PostgresEngine also available


Cloud SQL for MySQL
Chat memory using Cloud SQL for MySQL.

Install the python package:

pip install langchain-google-cloud-sql-mysql

See usage example.

from langchain_google_cloud_sql_mysql import MySQLChatMessageHistory # MySQLEngine also available

Cloud SQL for SQL Server
Chat memory using Cloud SQL for SQL Server.

Install the python package:

pip install langchain-google-cloud-sql-mssql

See usage example.

from langchain_google_cloud_sql_mssql import MSSQLChatMessageHistory # MSSQLEngine also available

Spanner
Chat memory using Cloud Spanner.

Install the python package:

pip install langchain-google-spanner

See usage example.

from langchain_google_spanner import SpannerChatMessageHistory

Memorystore for Redis
Chat memory using Memorystore for Redis.

Install the python package:

pip install langchain-google-memorystore-redis

See usage example.

from langchain_google_memorystore_redis import MemorystoreChatMessageHistory

Bigtable
Chat memory using Cloud Bigtable.

Install the python package:

pip install langchain-google-bigtable

See usage example.

from langchain_google_bigtable import BigtableChatMessageHistory

Firestore (Native Mode)
Chat memory using Firestore.

Install the python package:

pip install langchain-google-firestore

See usage example.

from langchain_google_firestore import FirestoreChatMessageHistory

Firestore (Datastore Mode)
Chat memory using Firestore in Datastore mode.

Install the python package:

pip install langchain-google-datastore

See usage example.

from langchain_google_datastore import DatastoreChatMessageHistory

El Carro: The Oracle Operator for Kubernetes
Chat memory using Oracle databases run via El Carro.

Install the python package:

pip install langchain-google-el-carro

See usage example.

from langchain_google_el_carro import ElCarroChatMessageHistory

Callbacks
Track LLM/Chat model usage.

Vertex AI callback handler
Callback Handler that tracks VertexAI usage info.

Requires langchain-google-vertexai.

from langchain_google_vertexai.callbacks import VertexAICallbackHandler

API Reference:VertexAICallbackHandler
Evaluators
Evaluate model outputs using Vertex AI.

Requires langchain-google-vertexai.

VertexPairWiseStringEvaluator
Pair-wise evaluation using Vertex AI models.

from langchain_google_vertexai.evaluators.evaluation import VertexPairWiseStringEvaluator

API Reference:VertexPairWiseStringEvaluator
VertexStringEvaluator
Evaluate a single prediction string using Vertex AI models.

# Note: Original doc listed VertexPairWiseStringEvaluator twice. Assuming this class exists.
from langchain_google_vertexai.evaluators.evaluation import VertexStringEvaluator # Verify class name if needed


API Reference:VertexStringEvaluator
Other Google Products
Integrations with various Google services beyond the core Cloud Platform.

Document Loaders
Google Drive
Google Drive file storage. Currently supports Google Docs.

Install with Drive dependencies:

pip install langchain-google-community[drive]

See usage example and authorization instructions.

from langchain_google_community import GoogleDriveLoader

API Reference:GoogleDriveLoader
Vector Stores
ScaNN (Local Index)
Google ScaNN (Scalable Nearest Neighbors) is a python package.

ScaNN is a method for efficient vector similarity search at scale.

ScaNN includes search space pruning and quantization for Maximum Inner Product Search and also supports other distance functions such as Euclidean distance. The implementation is optimized for x86 processors with AVX2 support. See its Google Research github for more details.

Install the scann package:

pip install scann langchain-community # Requires langchain-community

See a usage example.

from langchain_community.vectorstores import ScaNN

API Reference:ScaNN
Retrievers
Google Drive
Retrieve documents from Google Drive.

Install required packages:

pip install google-api-python-client google-auth-httplib2 google-auth-oauthlib langchain-googledrive

See usage example and authorization instructions.

from langchain_googledrive.retrievers import GoogleDriveRetriever

Tools
Google Drive
Tools for interacting with Google Drive.

Install required packages:

pip install google-api-python-client google-auth-httplib2 google-auth-oauthlib langchain-googledrive

See usage example and authorization instructions.

from langchain_googledrive.utilities.google_drive import GoogleDriveAPIWrapper
from langchain_googledrive.tools.google_drive.tool import GoogleDriveSearchTool

Google Finance
Query financial data. Requires google-search-results package and SerpApi key.

pip install google-search-results langchain-community # Requires langchain-community

See usage example and authorization instructions.

from langchain_community.tools.google_finance import GoogleFinanceQueryRun
from langchain_community.utilities.google_finance import GoogleFinanceAPIWrapper

API Reference:GoogleFinanceQueryRun | GoogleFinanceAPIWrapper
Google Jobs
Query job listings. Requires google-search-results package and SerpApi key.

pip install google-search-results langchain-community # Requires langchain-community

See usage example and authorization instructions.

from langchain_community.tools.google_jobs import GoogleJobsQueryRun
# Note: Utilities might be shared, e.g., GoogleFinanceAPIWrapper was listed, verify correct utility
# from langchain_community.utilities.google_jobs import GoogleJobsAPIWrapper # If exists

API Reference:GoogleJobsQueryRun | GoogleJobsAPIWrapper
Google Lens
Perform visual searches. Requires google-search-results package and SerpApi key.

pip install google-search-results langchain-community # Requires langchain-community

See usage example and authorization instructions.

from langchain_community.tools.google_lens import GoogleLensQueryRun
from langchain_community.utilities.google_lens import GoogleLensAPIWrapper

API Reference:GoogleLensQueryRun | GoogleLensAPIWrapper
Google Places
Search for places information. Requires googlemaps package and a Google Maps API key.

pip install googlemaps langchain # Requires base langchain

See usage example and authorization instructions.

# Note: GooglePlacesTool might be in langchain or langchain_community depending on version
from langchain.tools import GooglePlacesTool # Or langchain_community.tools

API Reference:GooglePlacesTool
Google Scholar
Search academic papers. Requires google-search-results package and SerpApi key.

pip install google-search-results langchain-community # Requires langchain-community

See usage example and authorization instructions.

from langchain_community.tools.google_scholar import GoogleScholarQueryRun
from langchain_community.utilities.google_scholar import GoogleScholarAPIWrapper

API Reference:GoogleScholarQueryRun | GoogleScholarAPIWrapper
Google Search
Perform web searches using Google Custom Search Engine (CSE). Requires GOOGLE_API_KEY and GOOGLE_CSE_ID.

Install langchain-google-community:

pip install langchain-google-community

Wrapper:

from langchain_google_community import GoogleSearchAPIWrapper

API Reference:GoogleSearchAPIWrapper
Tools:

from langchain_community.tools import GoogleSearchRun, GoogleSearchResults

API Reference:GoogleSearchRun | GoogleSearchResults
Agent Loading:

from langchain_community.agent_toolkits.load_tools import load_tools
tools = load_tools(["google-search"])

API Reference:load_tools
See detailed notebook.

Google Trends
Query Google Trends data. Requires google-search-results package and SerpApi key.

pip install google-search-results langchain-community # Requires langchain-community

See usage example and authorization instructions.

from langchain_community.tools.google_trends import GoogleTrendsQueryRun
from langchain_community.utilities.google_trends import GoogleTrendsAPIWrapper

API Reference:GoogleTrendsQueryRun | GoogleTrendsAPIWrapper
Toolkits
GMail
Google Gmail is a free email service provided by Google. This toolkit works with emails through the Gmail API.

pip install langchain-google-community[gmail]

See usage example and authorization instructions.

# Load the whole toolkit
from langchain_google_community import GmailToolkit

# Or use individual tools
from langchain_google_community.gmail.create_draft import GmailCreateDraft
from langchain_google_community.gmail.get_message import GmailGetMessage
from langchain_google_community.gmail.get_thread import GmailGetThread
from langchain_google_community.gmail.search import GmailSearch
from langchain_google_community.gmail.send_message import GmailSendMessage

API Reference:GmailToolkit | GmailCreateDraft | GmailGetMessage | GmailGetThread | GmailSearch | GmailSendMessage
Chat Loaders
GMail
Load chat history from Gmail threads.

Install with GMail dependencies:

pip install langchain-google-community[gmail]

See usage example and authorization instructions.

from langchain_google_community import GMailLoader

API Reference:GMailLoader
3rd Party Integrations
Access Google services via third-party APIs.

SearchApi
SearchApi provides API access to Google search, YouTube, etc. Requires langchain-community.

See usage examples and authorization instructions.

from langchain_community.utilities import SearchApiAPIWrapper

API Reference:SearchApiAPIWrapper
SerpApi
SerpApi provides API access to Google search results. Requires langchain-community.

See a usage example and authorization instructions.

from langchain_community.utilities import SerpAPIWrapper

API Reference:SerpAPIWrapper
Serper.dev
Google Serper provides API access to Google search results. Requires langchain-community.

See a usage example and authorization instructions.

from langchain_community.utilities import GoogleSerperAPIWrapper

API Reference:GoogleSerperAPIWrapper
YouTube
YouTube Search Tool
Search YouTube videos without the official API. Requires youtube_search package.

pip install youtube_search langchain # Requires base langchain

See a usage example.

# Note: YouTubeSearchTool might be in langchain or langchain_community
from langchain.tools import YouTubeSearchTool # Or langchain_community.tools

API Reference:YouTubeSearchTool
YouTube Audio Loader
Download audio from YouTube videos. Requires yt_dlp, pydub, librosa.

pip install yt_dlp pydub librosa langchain-community # Requires langchain-community

See usage example and authorization instructions.

from langchain_community.document_loaders.blob_loaders.youtube_audio import YoutubeAudioLoader
# Often used with whisper parsers:
# from langchain_community.document_loaders.parsers import OpenAIWhisperParser, OpenAIWhisperParserLocal


API Reference:YoutubeAudioLoader | OpenAIWhisperParser
YouTube Transcripts Loader
Load video transcripts. Requires youtube-transcript-api.

pip install youtube-transcript-api langchain-community # Requires langchain-community

See a usage example.

from langchain_community.document_loaders import YoutubeLoader

API Reference:YoutubeLoader
Edit this page
Was this page helpful?
