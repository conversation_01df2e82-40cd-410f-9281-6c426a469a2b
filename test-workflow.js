#!/usr/bin/env node

/**
 * YouTube Knowledge Graph Multi-Agent System Test Suite
 * 
 * This test file validates the workflow functionality by testing:
 * 1. Environment variable configuration
 * 2. Google Gemini API connectivity
 * 3. Memgraph database connectivity
 * 4. YouTube URL validation
 * 5. Knowledge graph structure validation
 */

const axios = require('axios');
require('dotenv').config();

class WorkflowTester {
  constructor() {
    this.testResults = {
      totalTests: 0,
      passed: 0,
      failed: 0,
      details: []
    };
  }

  addTest(testName, passed, details) {
    this.testResults.totalTests++;
    if (passed) {
      this.testResults.passed++;
      console.log(`✅ ${testName}: PASSED`);
    } else {
      this.testResults.failed++;
      console.log(`❌ ${testName}: FAILED - ${details}`);
    }
    
    this.testResults.details.push({
      test: testName,
      status: passed ? 'PASSED' : 'FAILED',
      details: details,
      timestamp: new Date().toISOString()
    });
  }

  async testEnvironmentVariables() {
    console.log('\n🔧 Testing Environment Variables...');
    
    const requiredVars = ['GOOGLE_API_KEY'];
    const optionalVars = ['MEMGRAPH_URI', 'MEMGRAPH_USERNAME', 'MEMGRAPH_PASSWORD'];
    
    let allRequiredPresent = true;
    const missingRequired = [];
    
    requiredVars.forEach(varName => {
      if (!process.env[varName]) {
        allRequiredPresent = false;
        missingRequired.push(varName);
      }
    });

    this.addTest(
      'Required Environment Variables',
      allRequiredPresent,
      allRequiredPresent ? 'All required variables are set' : `Missing: ${missingRequired.join(', ')}`
    );

    // Check optional variables
    optionalVars.forEach(varName => {
      const isSet = !!process.env[varName];
      this.addTest(
        `Optional Variable: ${varName}`,
        true, // Optional variables don't fail the test
        isSet ? 'Set' : 'Using default value'
      );
    });

    return allRequiredPresent;
  }

  async testGeminiAPIConnection() {
    console.log('\n🤖 Testing Google Gemini API Connection...');
    
    try {
      const apiKey = process.env.GOOGLE_API_KEY;
      if (!apiKey) {
        this.addTest('Gemini API Connection', false, 'API key not available');
        return false;
      }

      const response = await axios.post(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`,
        {
          contents: [{
            parts: [{
              text: 'Hello, this is a test message. Please respond with "API connection successful".'
            }]
          }]
        },
        {
          headers: { 'Content-Type': 'application/json' },
          timeout: 10000
        }
      );

      const success = response.status === 200;
      this.addTest(
        'Gemini API Connection',
        success,
        success ? 'API connection successful' : `HTTP ${response.status}`
      );
      return success;
    } catch (error) {
      this.addTest('Gemini API Connection', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testMemgraphConnection() {
    console.log('\n🗄️ Testing Memgraph Database Connection...');
    
    try {
      const uri = process.env.MEMGRAPH_URI || 'bolt://localhost:7687';
      const httpUri = uri.replace('bolt://', 'http://').replace(':7687', ':7444');
      
      const response = await axios.post(
        `${httpUri}/db/data/transaction/commit`,
        {
          statements: [{
            statement: 'RETURN 1 as test',
            parameters: {}
          }]
        },
        {
          headers: { 'Content-Type': 'application/json' },
          timeout: 5000
        }
      );

      const success = response.status === 200;
      this.addTest(
        'Memgraph Database Connection',
        success,
        success ? 'Database connection successful' : `HTTP ${response.status}`
      );
      return success;
    } catch (error) {
      this.addTest('Memgraph Database Connection', false, `Error: ${error.message}`);
      return false;
    }
  }

  testYouTubeURLValidation() {
    console.log('\n📺 Testing YouTube URL Validation...');
    
    const testUrls = [
      { url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', valid: true },
      { url: 'https://youtu.be/dQw4w9WgXcQ', valid: true },
      { url: 'https://youtube.com/watch?v=dQw4w9WgXcQ', valid: true },
      { url: 'https://www.google.com', valid: false },
      { url: 'invalid-url', valid: false }
    ];

    function isValidYouTubeUrl(url) {
      const regex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+/;
      return regex.test(url);
    }

    let allTestsPassed = true;
    testUrls.forEach(({ url, valid }) => {
      const result = isValidYouTubeUrl(url);
      const passed = result === valid;
      if (!passed) allTestsPassed = false;
      
      this.addTest(
        `YouTube URL Validation: ${url}`,
        passed,
        passed ? `Correctly identified as ${valid ? 'valid' : 'invalid'}` : `Expected ${valid}, got ${result}`
      );
    });

    return allTestsPassed;
  }

  testKnowledgeGraphStructure() {
    console.log('\n🕸️ Testing Knowledge Graph Structure...');
    
    // Test sample knowledge graph data structure
    const sampleKnowledgeData = {
      entities: [
        { name: 'John Doe', type: 'Person' },
        { name: 'Tech Corp', type: 'Organization' }
      ],
      relationships: [
        { from: 'John Doe', to: 'Tech Corp', type: 'WORKS_FOR' }
      ],
      knowledge: [
        { domain: 'Machine Learning', description: 'AI and ML expertise' }
      ]
    };

    let structureValid = true;
    const issues = [];

    // Validate structure
    if (!Array.isArray(sampleKnowledgeData.entities)) {
      structureValid = false;
      issues.push('entities should be an array');
    }

    if (!Array.isArray(sampleKnowledgeData.relationships)) {
      structureValid = false;
      issues.push('relationships should be an array');
    }

    if (!Array.isArray(sampleKnowledgeData.knowledge)) {
      structureValid = false;
      issues.push('knowledge should be an array');
    }

    this.addTest(
      'Knowledge Graph Structure',
      structureValid,
      structureValid ? 'Knowledge graph structure is valid' : issues.join('; ')
    );

    return structureValid;
  }

  async runAllTests() {
    console.log('🚀 Starting YouTube Knowledge Graph Multi-Agent System Tests...\n');
    
    const envTest = await this.testEnvironmentVariables();
    const geminiTest = await this.testGeminiAPIConnection();
    const memgraphTest = await this.testMemgraphConnection();
    const urlTest = this.testYouTubeURLValidation();
    const structureTest = this.testKnowledgeGraphStructure();
    
    return this.testResults;
  }

  generateReport() {
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    console.log(`Total Tests: ${this.testResults.totalTests}`);
    console.log(`Passed: ${this.testResults.passed}`);
    console.log(`Failed: ${this.testResults.failed}`);
    console.log(`Success Rate: ${((this.testResults.passed / this.testResults.totalTests) * 100).toFixed(1)}%`);
    
    if (this.testResults.failed === 0) {
      console.log('\n🎉 All tests passed! The workflow is ready to use.');
    } else {
      console.log('\n⚠️ Some tests failed. Please review the configuration and try again.');
      console.log('\nRecommendations:');
      if (this.testResults.details.some(d => d.test.includes('GOOGLE_API_KEY'))) {
        console.log('- Set your GOOGLE_API_KEY environment variable');
      }
      if (this.testResults.details.some(d => d.test.includes('Memgraph'))) {
        console.log('- Ensure Memgraph database is running on the specified port');
      }
    }
    
    return this.testResults;
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new WorkflowTester();
  
  tester.runAllTests()
    .then(() => {
      const results = tester.generateReport();
      process.exit(results.failed === 0 ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error.message);
      process.exit(1);
    });
}

module.exports = WorkflowTester;
