{"meta": {"templateCredsSetupCompleted": false, "instanceId": "youtube-knowledge-graph-system"}, "name": "YouTube Knowledge Graph Multi-Agent System", "active": false, "nodes": [{"parameters": {"options": {}}, "id": "chat-trigger", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.chatTrigger", "typeVersion": 1, "position": [240, 200]}, {"parameters": {}, "id": "workflow-trigger", "name": "Workflow Trigger", "type": "n8n-nodes-base.workflowTrigger", "typeVersion": 1, "position": [240, 400]}, {"parameters": {"values": {"string": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "value": "YOUR_GOOGLE_API_KEY_HERE"}]}, "options": {}}, "id": "set-api-key", "name": "Set API Key", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [460, 300]}, {"parameters": {"jsCode": "// Input Processor Agent - Parse and validate YouTube URLs from input\nconst inputData = $input.all();\nlet inputText = '';\n\n// Extract input text from different trigger types\nif (inputData.length > 0) {\n  const firstItem = inputData[0].json;\n  \n  // From Chat Trigger\n  if (firstItem.chatInput) {\n    inputText = firstItem.chatInput;\n  }\n  // From Workflow Trigger\n  else if (firstItem.youtubeUrls) {\n    inputText = Array.isArray(firstItem.youtubeUrls) ? firstItem.youtubeUrls.join(' ') : firstItem.youtubeUrls;\n  }\n  // From manual input or other sources\n  else if (firstItem.input) {\n    inputText = firstItem.input;\n  }\n  // Fallback to any text content\n  else {\n    inputText = JSON.stringify(firstItem);\n  }\n}\n\n// Extract YouTube URLs using regex\nconst urlRegex = /(?:https?:\\/\\/)?(?:www\\.)?(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([a-zA-Z0-9_-]{11})/g;\nconst foundUrls = [];\nlet match;\n\nwhile ((match = urlRegex.exec(inputText)) !== null) {\n  const fullUrl = match[0].startsWith('http') ? match[0] : `https://www.youtube.com/watch?v=${match[1]}`;\n  foundUrls.push(fullUrl);\n}\n\n// If no URLs found in input, use default test URLs\nconst youtubeUrls = foundUrls.length > 0 ? foundUrls : [\n  'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n  'https://www.youtube.com/watch?v=9bZkp7q19f0',\n  'https://www.youtube.com/watch?v=kJQP7kiw5Fk'\n];\n\n// Validate YouTube URLs\nfunction isValidYouTubeUrl(url) {\n  const regex = /^(https?\\:\\/\\/)?(www\\.)?(youtube\\.com|youtu\\.be)\\/.+/;\n  return regex.test(url);\n}\n\nconst validUrls = youtubeUrls.filter(url => {\n  if (!isValidYouTubeUrl(url)) {\n    console.log(`Invalid YouTube URL: ${url}`);\n    return false;\n  }\n  return true;\n});\n\nconsole.log(`Processing ${validUrls.length} valid YouTube URLs`);\nconsole.log('URLs to process:', validUrls);\n\nreturn validUrls.map(url => ({\n  json: {\n    youtubeUrl: url,\n    timestamp: new Date().toISOString(),\n    status: 'ready_for_analysis',\n    apiKey: $node[\"Set API Key\"].json[\"apiKey\"]\n  }\n}));"}, "id": "input-processor", "name": "Input Processor Agent", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"batchSize": 1, "options": {}}, "id": "split-videos", "name": "Split Videos", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [900, 300]}, {"parameters": {"jsCode": "// Video Analysis Agent - Analyze YouTube videos with Gemini 2.5 Flash\nconst axios = require('axios');\n\nasync function analyzeVideoWithGemini(youtubeUrl, apiKey) {\n  if (!apiKey || apiKey === 'YOUR_GOOGLE_API_KEY_HERE') {\n    throw new Error('Valid Google API key is required. Please update the Set API Key node.');\n  }\n\n  const prompt = `Analyze this YouTube video and identify all people/personas mentioned or appearing. For each person, provide:\n1. Full name\n2. Role or title\n3. Brief description\n4. Key topics they discuss\n5. Their expertise areas\n\nStructure your response as a JSON array of person objects with the following format:\n[{\n  \"name\": \"Person Name\",\n  \"role\": \"Their Role\",\n  \"description\": \"Brief description\",\n  \"topics\": [\"topic1\", \"topic2\"],\n  \"expertise\": [\"skill1\", \"skill2\"]\n}]`;\n\n  try {\n    const response = await axios.post(\n      `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`,\n      {\n        contents: [{\n          parts: [\n            {\n              file_data: {\n                file_uri: youtubeUrl\n              }\n            },\n            {\n              text: prompt\n            }\n          ]\n        }],\n        generationConfig: {\n          temperature: 0.1,\n          topK: 40,\n          topP: 0.95,\n          maxOutputTokens: 8192\n        }\n      },\n      {\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      }\n    );\n\n    const content = response.data.candidates[0].content.parts[0].text;\n    let personas;\n    \n    try {\n      // Extract JSON from the response\n      const jsonMatch = content.match(/\\[.*\\]/s);\n      personas = jsonMatch ? JSON.parse(jsonMatch[0]) : [];\n    } catch (parseError) {\n      console.log('Failed to parse JSON, using fallback extraction');\n      personas = [{\n        name: 'Unknown Speaker',\n        role: 'Video Participant',\n        description: content.substring(0, 200),\n        topics: ['General Discussion'],\n        expertise: ['Communication']\n      }];\n    }\n\n    return {\n      youtubeUrl,\n      personas,\n      analysisTimestamp: new Date().toISOString(),\n      status: 'analysis_complete',\n      apiKey: apiKey\n    };\n  } catch (error) {\n    console.error(`Error analyzing video ${youtubeUrl}:`, error.message);\n    return {\n      youtubeUrl,\n      personas: [],\n      error: error.message,\n      status: 'analysis_failed',\n      apiKey: apiKey\n    };\n  }\n}\n\n// Process the current batch item\nconst inputData = $input.all();\nconst currentItem = inputData[0].json;\n\nconsole.log(`Analyzing video: ${currentItem.youtubeUrl}`);\n\nconst result = await analyzeVideoWithGemini(currentItem.youtubeUrl, currentItem.apiKey);\n\nreturn [{ json: result }];"}, "id": "video-analysis-agent", "name": "Video Analysis Agent", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"batchSize": 1, "options": {}}, "id": "split-personas", "name": "Split Personas", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1340, 300]}, {"parameters": {"jsCode": "// Prepare Personas for Processing - Convert personas array to individual items\nconst inputData = $input.all();\nconst videoData = inputData[0].json;\n\nif (!videoData.personas || videoData.personas.length === 0) {\n  console.log('No personas found in video analysis');\n  return [{\n    json: {\n      youtubeUrl: videoData.youtubeUrl,\n      persona: null,\n      apiKey: videoData.apiKey,\n      status: 'no_personas_found',\n      timestamp: new Date().toISOString()\n    }\n  }];\n}\n\nconsole.log(`Found ${videoData.personas.length} personas in video: ${videoData.youtubeUrl}`);\n\n// Create individual items for each persona\nconst personaItems = videoData.personas.map(persona => ({\n  json: {\n    youtubeUrl: videoData.youtubeUrl,\n    persona: persona,\n    apiKey: videoData.apiKey,\n    analysisTimestamp: videoData.analysisTimestamp,\n    status: 'ready_for_knowledge_extraction',\n    timestamp: new Date().toISOString()\n  }\n}));\n\nreturn personaItems;"}, "id": "prepare-personas", "name": "Prepare <PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 300]}, {"parameters": {"jsCode": "// Knowledge Extraction Agent - Extract detailed knowledge for each persona\nconst axios = require('axios');\n\nasync function extractKnowledgeForPersona(persona, youtubeUrl, apiKey) {\n  if (!apiKey || apiKey === 'YOUR_GOOGLE_API_KEY_HERE') {\n    throw new Error('Valid Google API key is required. Please update the Set API Key node.');\n  }\n\n  const prompt = `For the person \"${persona.name}\" (${persona.role}) from the video ${youtubeUrl}, extract comprehensive knowledge including:\n\n1. ENTITIES:\n   - People they mention or work with\n   - Organizations they're affiliated with\n   - Concepts, technologies, or methodologies they discuss\n   - Projects or products they're involved in\n\n2. RELATIONSHIPS:\n   - Professional relationships\n   - Collaborations\n   - Influences or mentorships\n   - Organizational connections\n\n3. KNOWLEDGE AREAS:\n   - Expertise domains\n   - Skills and competencies\n   - Research interests\n   - Industry knowledge\n\nStructure your response as JSON with the following format:\n{\n  \"entities\": [\n    {\"name\": \"Entity Name\", \"type\": \"Person|Organization|Concept\", \"description\": \"Brief description\"}\n  ],\n  \"relationships\": [\n    {\"from\": \"Source\", \"to\": \"Target\", \"type\": \"RELATIONSHIP_TYPE\", \"description\": \"Description\"}\n  ],\n  \"knowledge\": [\n    {\"domain\": \"Knowledge Domain\", \"description\": \"Detailed description\", \"level\": \"Beginner|Intermediate|Expert\"}\n  ]\n}`;\n\n  try {\n    const response = await axios.post(\n      `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`,\n      {\n        contents: [{\n          parts: [{\n            text: prompt\n          }]\n        }],\n        generationConfig: {\n          temperature: 0.1,\n          topK: 40,\n          topP: 0.95,\n          maxOutputTokens: 8192\n        }\n      },\n      {\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      }\n    );\n\n    const content = response.data.candidates[0].content.parts[0].text;\n    let knowledgeData;\n    \n    try {\n      const jsonMatch = content.match(/\\{.*\\}/s);\n      knowledgeData = jsonMatch ? JSON.parse(jsonMatch[0]) : {\n        entities: [],\n        relationships: [],\n        knowledge: []\n      };\n    } catch (parseError) {\n      console.log('Failed to parse knowledge JSON, using fallback');\n      knowledgeData = {\n        entities: [{ name: persona.name, type: 'Person', description: persona.description }],\n        relationships: [],\n        knowledge: (persona.expertise || []).map(exp => ({ domain: exp, description: '', level: 'Unknown' }))\n      };\n    }\n\n    return {\n      persona: persona,\n      youtubeUrl: youtubeUrl,\n      extractedKnowledge: knowledgeData,\n      extractionTimestamp: new Date().toISOString(),\n      status: 'extraction_complete',\n      apiKey: apiKey\n    };\n  } catch (error) {\n    console.error(`Error extracting knowledge for ${persona.name}:`, error.message);\n    return {\n      persona: persona,\n      youtubeUrl: youtubeUrl,\n      extractedKnowledge: { entities: [], relationships: [], knowledge: [] },\n      error: error.message,\n      status: 'extraction_failed',\n      apiKey: apiKey\n    };\n  }\n}\n\n// Process the current persona\nconst inputData = $input.all();\nconst currentItem = inputData[0].json;\n\nif (!currentItem.persona) {\n  return [{\n    json: {\n      ...currentItem,\n      status: 'skipped_no_persona'\n    }\n  }];\n}\n\nconsole.log(`Extracting knowledge for persona: ${currentItem.persona.name}`);\n\nconst result = await extractKnowledgeForPersona(\n  currentItem.persona, \n  currentItem.youtubeUrl, \n  currentItem.apiKey\n);\n\nreturn [{ json: result }];"}, "id": "knowledge-extraction-agent", "name": "Knowledge Extraction Agent", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 300]}, {"parameters": {"jsCode": "// Memgraph Storage Agent - Store knowledge graph in Ai-personas collection\nconst axios = require('axios');\n\nclass MemgraphStorage {\n  constructor() {\n    // Default Docker Memgraph configuration\n    this.httpUri = 'http://localhost:7444';\n    this.boltUri = 'bolt://localhost:7687';\n    this.username = ''; // Docker Memgraph typically has no auth by default\n    this.password = '';\n    this.collection = 'Ai-personas';\n  }\n\n  async executeCypher(query, parameters = {}) {\n    try {\n      // Try multiple endpoints for Memgraph HTTP API\n      const endpoints = [\n        `${this.httpUri}/db/data/transaction/commit`,\n        `${this.httpUri}/db/data/cypher`,\n        `${this.httpUri}/query`\n      ];\n      \n      let lastError;\n      \n      for (const endpoint of endpoints) {\n        try {\n          const response = await axios.post(\n            endpoint,\n            {\n              statements: [{\n                statement: query,\n                parameters: parameters\n              }]\n            },\n            {\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              timeout: 10000\n            }\n          );\n          \n          if (response.status === 200) {\n            return response.data;\n          }\n        } catch (error) {\n          lastError = error;\n          console.log(`Failed endpoint ${endpoint}: ${error.message}`);\n          continue;\n        }\n      }\n      \n      throw lastError || new Error('All Memgraph endpoints failed');\n    } catch (error) {\n      console.error('Cypher execution error:', error.message);\n      throw error;\n    }\n  }\n\n  generateCypherQueries(knowledgeData) {\n    const queries = [];\n    const { persona, youtubeUrl, extractedKnowledge } = knowledgeData;\n    const timestamp = new Date().toISOString();\n    \n    // Use the Ai-personas collection\n    const collectionPrefix = '';\n\n    // Create Video node in Ai-personas collection\n    queries.push({\n      query: `MERGE (v:Video:AiPersonas {url: $url}) \n              SET v.processedAt = $timestamp, v.collection = $collection`,\n      params: { \n        url: youtubeUrl, \n        timestamp: timestamp,\n        collection: this.collection\n      }\n    });\n\n    // Create Person node in Ai-personas collection\n    queries.push({\n      query: `MERGE (p:Person:AiPersonas {name: $name}) \n              SET p.role = $role, p.description = $description, p.topics = $topics, \n                  p.expertise = $expertise, p.collection = $collection, p.updatedAt = $timestamp`,\n      params: {\n        name: persona.name,\n        role: persona.role || 'Unknown',\n        description: persona.description || '',\n        topics: persona.topics || [],\n        expertise: persona.expertise || [],\n        collection: this.collection,\n        timestamp: timestamp\n      }\n    });\n\n    // Create APPEARS_IN relationship\n    queries.push({\n      query: `MATCH (p:Person:AiPersonas {name: $personName}), (v:Video:AiPersonas {url: $videoUrl})\n              MERGE (p)-[:APPEARS_IN {collection: $collection, createdAt: $timestamp}]->(v)`,\n      params: { \n        personName: persona.name, \n        videoUrl: youtubeUrl,\n        collection: this.collection,\n        timestamp: timestamp\n      }\n    });\n\n    // Create entities and relationships\n    if (extractedKnowledge.entities) {\n      extractedKnowledge.entities.forEach(entity => {\n        const entityType = entity.type || 'Entity';\n        \n        // Create entity node\n        queries.push({\n          query: `MERGE (e:${entityType}:AiPersonas {name: $name}) \n                  SET e.description = $description, e.collection = $collection, e.updatedAt = $timestamp`,\n          params: { \n            name: entity.name, \n            description: entity.description || '',\n            collection: this.collection,\n            timestamp: timestamp\n          }\n        });\n\n        // Create relationship between person and entity\n        queries.push({\n          query: `MATCH (p:Person:AiPersonas {name: $personName}), (e:${entityType}:AiPersonas {name: $entityName})\n                  MERGE (p)-[:MENTIONS {collection: $collection, createdAt: $timestamp}]->(e)`,\n          params: { \n            personName: persona.name, \n            entityName: entity.name,\n            collection: this.collection,\n            timestamp: timestamp\n          }\n        });\n      });\n    }\n\n    // Create knowledge nodes and relationships\n    if (extractedKnowledge.knowledge) {\n      extractedKnowledge.knowledge.forEach(knowledge => {\n        const domain = knowledge.domain || knowledge;\n        \n        queries.push({\n          query: `MERGE (k:Knowledge:AiPersonas {domain: $domain}) \n                  SET k.description = $description, k.level = $level, k.collection = $collection, k.updatedAt = $timestamp`,\n          params: { \n            domain: domain, \n            description: knowledge.description || '',\n            level: knowledge.level || 'Unknown',\n            collection: this.collection,\n            timestamp: timestamp\n          }\n        });\n\n        queries.push({\n          query: `MATCH (p:Person:AiPersonas {name: $personName}), (k:Knowledge:AiPersonas {domain: $domain})\n                  MERGE (p)-[:HAS_EXPERTISE {collection: $collection, level: $level, createdAt: $timestamp}]->(k)`,\n          params: { \n            personName: persona.name, \n            domain: domain,\n            level: knowledge.level || 'Unknown',\n            collection: this.collection,\n            timestamp: timestamp\n          }\n        });\n      });\n    }\n\n    // Create custom relationships from extracted data\n    if (extractedKnowledge.relationships) {\n      extractedKnowledge.relationships.forEach(rel => {\n        queries.push({\n          query: `MATCH (from:AiPersonas {name: $fromName}), (to:AiPersonas {name: $toName})\n                  MERGE (from)-[:${rel.type || 'RELATED_TO'} {description: $description, collection: $collection, createdAt: $timestamp}]->(to)`,\n          params: {\n            fromName: rel.from,\n            toName: rel.to,\n            description: rel.description || '',\n            collection: this.collection,\n            timestamp: timestamp\n          }\n        });\n      });\n    }\n\n    return queries;\n  }\n\n  async storeKnowledgeGraph(knowledgeData) {\n    try {\n      const queries = this.generateCypherQueries(knowledgeData);\n      const results = [];\n      let successCount = 0;\n\n      console.log(`Executing ${queries.length} Cypher queries for ${knowledgeData.persona.name}`);\n\n      for (const { query, params } of queries) {\n        try {\n          const result = await this.executeCypher(query, params);\n          results.push({ query, params, result, success: true });\n          successCount++;\n        } catch (error) {\n          console.error(`Query failed: ${query}`, error.message);\n          results.push({ query, params, error: error.message, success: false });\n        }\n      }\n\n      return {\n        success: successCount > 0,\n        queriesExecuted: queries.length,\n        successfulQueries: successCount,\n        failedQueries: queries.length - successCount,\n        results: results,\n        collection: this.collection\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message,\n        queriesExecuted: 0,\n        collection: this.collection\n      };\n    }\n  }\n}\n\n// Process the current knowledge extraction result\nconst inputData = $input.all();\nconst currentItem = inputData[0].json;\nconst storage = new MemgraphStorage();\n\nif (currentItem.status === 'extraction_complete') {\n  console.log(`Storing knowledge graph for persona: ${currentItem.persona.name}`);\n  \n  const storageResult = await storage.storeKnowledgeGraph(currentItem);\n  \n  return [{\n    json: {\n      ...currentItem,\n      storageResult: storageResult,\n      storageTimestamp: new Date().toISOString(),\n      status: storageResult.success ? 'stored_successfully' : 'storage_failed'\n    }\n  }];\n} else {\n  return [{\n    json: {\n      ...currentItem,\n      storageResult: { success: false, error: 'Knowledge extraction failed' },\n      status: 'storage_skipped'\n    }\n  }];\n}"}, "id": "memgraph-storage-agent", "name": "Memgraph Storage Agent", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2000, 300]}, {"parameters": {"jsCode": "// Comprehensive Test Suite - Validate entire workflow\nconst axios = require('axios');\n\nclass WorkflowTester {\n  constructor() {\n    this.testResults = {\n      totalTests: 0,\n      passed: 0,\n      failed: 0,\n      details: []\n    };\n  }\n\n  addTest(testName, passed, details) {\n    this.testResults.totalTests++;\n    if (passed) {\n      this.testResults.passed++;\n    } else {\n      this.testResults.failed++;\n    }\n    this.testResults.details.push({\n      test: testName,\n      status: passed ? 'PASSED' : 'FAILED',\n      details: details,\n      timestamp: new Date().toISOString()\n    });\n  }\n\n  async testEnvironmentVariables() {\n    const requiredVars = ['GOOGLE_API_KEY', 'MEMGRAPH_URI'];\n    let allPresent = true;\n    const missingVars = [];\n\n    requiredVars.forEach(varName => {\n      if (!process.env[varName]) {\n        allPresent = false;\n        missingVars.push(varName);\n      }\n    });\n\n    this.addTest(\n      'Environment Variables',\n      allPresent,\n      allPresent ? 'All required environment variables are set' : `Missing: ${missingVars.join(', ')}`\n    );\n\n    return allPresent;\n  }\n\n  async testGeminiAPIConnection() {\n    try {\n      const apiKey = process.env.GOOGLE_API_KEY;\n      if (!apiKey) {\n        this.addTest('Gemini API Connection', false, 'API key not available');\n        return false;\n      }\n\n      const response = await axios.post(\n        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`,\n        {\n          contents: [{\n            parts: [{\n              text: 'Hello, this is a test message. Please respond with \"API connection successful\".'\n            }]\n          }]\n        },\n        {\n          headers: { 'Content-Type': 'application/json' },\n          timeout: 10000\n        }\n      );\n\n      const success = response.status === 200;\n      this.addTest(\n        'Gemini API Connection',\n        success,\n        success ? 'API connection successful' : `HTTP ${response.status}`\n      );\n      return success;\n    } catch (error) {\n      this.addTest('Gemini API Connection', false, `Error: ${error.message}`);\n      return false;\n    }\n  }\n\n  async testMemgraphConnection() {\n    try {\n      const uri = process.env.MEMGRAPH_URI || 'bolt://localhost:7687';\n      const httpUri = uri.replace('bolt://', 'http://').replace(':7687', ':7444');\n      \n      const response = await axios.post(\n        `${httpUri}/db/data/transaction/commit`,\n        {\n          statements: [{\n            statement: 'RETURN 1 as test',\n            parameters: {}\n          }]\n        },\n        {\n          headers: { 'Content-Type': 'application/json' },\n          timeout: 5000\n        }\n      );\n\n      const success = response.status === 200;\n      this.addTest(\n        'Memgraph Database Connection',\n        success,\n        success ? 'Database connection successful' : `HTTP ${response.status}`\n      );\n      return success;\n    } catch (error) {\n      this.addTest('Memgraph Database Connection', false, `Error: ${error.message}`);\n      return false;\n    }\n  }\n\n  testDataIntegrity(inputData) {\n    let allValid = true;\n    const issues = [];\n\n    inputData.forEach((item, index) => {\n      const data = item.json;\n      \n      // Test video analysis results\n      if (!data.youtubeUrl) {\n        allValid = false;\n        issues.push(`Item ${index}: Missing YouTube URL`);\n      }\n      \n      if (!data.persona || !data.persona.name) {\n        allValid = false;\n        issues.push(`Item ${index}: Missing or invalid persona data`);\n      }\n      \n      if (!data.extractedKnowledge) {\n        allValid = false;\n        issues.push(`Item ${index}: Missing extracted knowledge`);\n      }\n      \n      if (!data.storageResult) {\n        allValid = false;\n        issues.push(`Item ${index}: Missing storage result`);\n      }\n    });\n\n    this.addTest(\n      'Data Integrity',\n      allValid,\n      allValid ? 'All data items are valid' : issues.join('; ')\n    );\n\n    return allValid;\n  }\n\n  testKnowledgeGraphStructure(inputData) {\n    let validStructure = true;\n    const structureIssues = [];\n\n    inputData.forEach((item, index) => {\n      const knowledge = item.json.extractedKnowledge;\n      \n      if (knowledge) {\n        if (!Array.isArray(knowledge.entities)) {\n          validStructure = false;\n          structureIssues.push(`Item ${index}: entities should be an array`);\n        }\n        \n        if (!Array.isArray(knowledge.relationships)) {\n          validStructure = false;\n          structureIssues.push(`Item ${index}: relationships should be an array`);\n        }\n        \n        if (!Array.isArray(knowledge.knowledge)) {\n          validStructure = false;\n          structureIssues.push(`Item ${index}: knowledge should be an array`);\n        }\n      }\n    });\n\n    this.addTest(\n      'Knowledge Graph Structure',\n      validStructure,\n      validStructure ? 'Knowledge graph structure is valid' : structureIssues.join('; ')\n    );\n\n    return validStructure;\n  }\n\n  async runAllTests(inputData) {\n    console.log('Starting comprehensive workflow tests...');\n    \n    await this.testEnvironmentVariables();\n    await this.testGeminiAPIConnection();\n    await this.testMemgraphConnection();\n    this.testDataIntegrity(inputData);\n    this.testKnowledgeGraphStructure(inputData);\n    \n    return this.testResults;\n  }\n}\n\n// Run comprehensive tests\nconst inputData = $input.all();\nconst tester = new WorkflowTester();\n\nconst testResults = await tester.runAllTests(inputData);\n\n// Generate summary report\nconst summary = {\n  workflowStatus: testResults.failed === 0 ? 'SUCCESS' : 'PARTIAL_SUCCESS',\n  testSummary: testResults,\n  processedItems: inputData.length,\n  successfullyStored: inputData.filter(item => item.json.status === 'stored_successfully').length,\n  timestamp: new Date().toISOString(),\n  recommendations: []\n};\n\n// Add recommendations based on test results\nif (testResults.failed > 0) {\n  summary.recommendations.push('Review failed tests and fix configuration issues');\n}\nif (summary.successfullyStored < inputData.length) {\n  summary.recommendations.push('Check storage failures and database connectivity');\n}\nif (summary.successfullyStored === 0) {\n  summary.recommendations.push('Verify API keys and database configuration');\n}\n\nreturn [{\n  json: {\n    ...summary,\n    inputData: inputData,\n    detailedResults: testResults.details\n  }\n}];"}, "id": "test-suite", "name": "Comprehensive Test Suite", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2220, 300]}], "connections": {"Chat Trigger": {"main": [[{"node": "Set API Key", "type": "main", "index": 0}]]}, "Workflow Trigger": {"main": [[{"node": "Set API Key", "type": "main", "index": 0}]]}, "Set API Key": {"main": [[{"node": "Input Processor Agent", "type": "main", "index": 0}]]}, "Input Processor Agent": {"main": [[{"node": "Split Videos", "type": "main", "index": 0}]]}, "Split Videos": {"main": [[{"node": "Video Analysis Agent", "type": "main", "index": 0}]]}, "Video Analysis Agent": {"main": [[{"node": "Split Personas", "type": "main", "index": 0}]]}, "Split Personas": {"main": [[{"node": "Prepare <PERSON>", "type": "main", "index": 0}]]}, "Prepare Personas": {"main": [[{"node": "Knowledge Extraction Agent", "type": "main", "index": 0}]]}, "Knowledge Extraction Agent": {"main": [[{"node": "Memgraph Storage Agent", "type": "main", "index": 0}]]}, "Memgraph Storage Agent": {"main": [[{"node": "Comprehensive Test Suite", "type": "main", "index": 0}]]}}, "pinData": {}}