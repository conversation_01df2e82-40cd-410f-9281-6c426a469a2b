{"meta": {"templateCredsSetupCompleted": false, "instanceId": "youtube-knowledge-graph-system"}, "name": "YouTube Knowledge Graph Multi-Agent System", "active": false, "nodes": [{"parameters": {}, "id": "manual-trigger-start", "name": "Start Workflow", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "// Input Processor Agent - Parse and validate YouTube URLs\nconst youtubeUrls = [\n  'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n  'https://www.youtube.com/watch?v=9bZkp7q19f0',\n  'https://www.youtube.com/watch?v=kJQP7kiw5Fk'\n];\n\n// Validate YouTube URLs\nfunction isValidYouTubeUrl(url) {\n  const regex = /^(https?\\:\\/\\/)?(www\\.)?(youtube\\.com|youtu\\.be)\\/.+/;\n  return regex.test(url);\n}\n\nconst validUrls = youtubeUrls.filter(url => {\n  if (!isValidYouTubeUrl(url)) {\n    console.log(`Invalid YouTube URL: ${url}`);\n    return false;\n  }\n  return true;\n});\n\nconsole.log(`Processing ${validUrls.length} valid YouTube URLs`);\n\nreturn validUrls.map(url => ({\n  json: {\n    youtubeUrl: url,\n    timestamp: new Date().toISOString(),\n    status: 'ready_for_analysis'\n  }\n}));"}, "id": "input-processor", "name": "Input Processor Agent", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"jsCode": "// Video Analysis Agent - Analyze YouTube videos with Gemini 2.5 Flash\nconst axios = require('axios');\n\nasync function analyzeVideoWithGemini(youtubeUrl) {\n  const apiKey = process.env.GOOGLE_API_KEY;\n  if (!apiKey) {\n    throw new Error('GOOGLE_API_KEY environment variable is required');\n  }\n\n  const prompt = `Analyze this YouTube video and identify all people/personas mentioned or appearing. For each person, provide:\n1. Full name\n2. Role or title\n3. Brief description\n4. Key topics they discuss\n5. Their expertise areas\n\nStructure your response as a JSON array of person objects.`;\n\n  try {\n    const response = await axios.post(\n      `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`,\n      {\n        contents: [{\n          parts: [\n            {\n              file_data: {\n                file_uri: youtubeUrl\n              }\n            },\n            {\n              text: prompt\n            }\n          ]\n        }],\n        generationConfig: {\n          temperature: 0.1,\n          topK: 40,\n          topP: 0.95,\n          maxOutputTokens: 8192\n        }\n      },\n      {\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      }\n    );\n\n    const content = response.data.candidates[0].content.parts[0].text;\n    let personas;\n    \n    try {\n      // Extract JSON from the response\n      const jsonMatch = content.match(/\\[.*\\]/s);\n      personas = jsonMatch ? JSON.parse(jsonMatch[0]) : [];\n    } catch (parseError) {\n      console.log('Failed to parse JSON, using fallback extraction');\n      personas = [{\n        name: 'Unknown Speaker',\n        role: 'Video Participant',\n        description: content.substring(0, 200),\n        topics: ['General Discussion'],\n        expertise: ['Communication']\n      }];\n    }\n\n    return {\n      youtubeUrl,\n      personas,\n      analysisTimestamp: new Date().toISOString(),\n      status: 'analysis_complete'\n    };\n  } catch (error) {\n    console.error(`Error analyzing video ${youtubeUrl}:`, error.message);\n    return {\n      youtubeUrl,\n      personas: [],\n      error: error.message,\n      status: 'analysis_failed'\n    };\n  }\n}\n\n// Process the current item\nconst inputData = $input.all();\nconst results = [];\n\nfor (const item of inputData) {\n  const result = await analyzeVideoWithGemini(item.json.youtubeUrl);\n  results.push({ json: result });\n}\n\nreturn results;"}, "id": "video-analysis-agent", "name": "Video Analysis Agent", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"jsCode": "// Knowledge Extraction Agent - Extract detailed knowledge for each persona\nconst axios = require('axios');\n\nasync function extractKnowledgeForPersona(persona, youtubeUrl) {\n  const apiKey = process.env.GOOGLE_API_KEY;\n  if (!apiKey) {\n    throw new Error('GOOGLE_API_KEY environment variable is required');\n  }\n\n  const prompt = `For the person \"${persona.name}\" (${persona.role}) from the video ${youtubeUrl}, extract comprehensive knowledge including:\n\n1. ENTITIES:\n   - People they mention or work with\n   - Organizations they're affiliated with\n   - Concepts, technologies, or methodologies they discuss\n   - Projects or products they're involved in\n\n2. RELATIONSHIPS:\n   - Professional relationships\n   - Collaborations\n   - Influences or mentorships\n   - Organizational connections\n\n3. KNOWLEDGE AREAS:\n   - Expertise domains\n   - Skills and competencies\n   - Research interests\n   - Industry knowledge\n\nStructure your response as JSON with entities, relationships, and knowledge arrays.`;\n\n  try {\n    const response = await axios.post(\n      `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`,\n      {\n        contents: [{\n          parts: [{\n            text: prompt\n          }]\n        }],\n        generationConfig: {\n          temperature: 0.1,\n          topK: 40,\n          topP: 0.95,\n          maxOutputTokens: 8192\n        }\n      },\n      {\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      }\n    );\n\n    const content = response.data.candidates[0].content.parts[0].text;\n    let knowledgeData;\n    \n    try {\n      const jsonMatch = content.match(/\\{.*\\}/s);\n      knowledgeData = jsonMatch ? JSON.parse(jsonMatch[0]) : {\n        entities: [],\n        relationships: [],\n        knowledge: []\n      };\n    } catch (parseError) {\n      console.log('Failed to parse knowledge JSON, using fallback');\n      knowledgeData = {\n        entities: [{ name: persona.name, type: 'Person' }],\n        relationships: [],\n        knowledge: persona.expertise || []\n      };\n    }\n\n    return {\n      persona: persona,\n      youtubeUrl: youtubeUrl,\n      extractedKnowledge: knowledgeData,\n      extractionTimestamp: new Date().toISOString(),\n      status: 'extraction_complete'\n    };\n  } catch (error) {\n    console.error(`Error extracting knowledge for ${persona.name}:`, error.message);\n    return {\n      persona: persona,\n      youtubeUrl: youtubeUrl,\n      extractedKnowledge: { entities: [], relationships: [], knowledge: [] },\n      error: error.message,\n      status: 'extraction_failed'\n    };\n  }\n}\n\n// Process all personas from video analysis\nconst inputData = $input.all();\nconst results = [];\n\nfor (const item of inputData) {\n  if (item.json.personas && item.json.personas.length > 0) {\n    for (const persona of item.json.personas) {\n      const result = await extractKnowledgeForPersona(persona, item.json.youtubeUrl);\n      results.push({ json: result });\n    }\n  }\n}\n\nreturn results;"}, "id": "knowledge-extraction-agent", "name": "Knowledge Extraction Agent", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"jsCode": "// Memgraph Storage Agent - Store knowledge graph in database\nconst axios = require('axios');\n\nclass MemgraphStorage {\n  constructor() {\n    this.uri = process.env.MEMGRAPH_URI || 'bolt://localhost:7687';\n    this.username = process.env.MEMGRAPH_USERNAME || '';\n    this.password = process.env.MEMGRAPH_PASSWORD || '';\n  }\n\n  async executeCypher(query, parameters = {}) {\n    try {\n      // Using HTTP endpoint for Memgraph\n      const response = await axios.post(\n        `${this.uri.replace('bolt://', 'http://').replace(':7687', ':7444')}/db/data/transaction/commit`,\n        {\n          statements: [{\n            statement: query,\n            parameters: parameters\n          }]\n        },\n        {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': this.username && this.password ? \n              `Basic ${Buffer.from(`${this.username}:${this.password}`).toString('base64')}` : undefined\n          }\n        }\n      );\n      return response.data;\n    } catch (error) {\n      console.error('Cypher execution error:', error.message);\n      throw error;\n    }\n  }\n\n  generateCypherQueries(knowledgeData) {\n    const queries = [];\n    const { persona, youtubeUrl, extractedKnowledge } = knowledgeData;\n\n    // Create Video node\n    queries.push({\n      query: `MERGE (v:Video {url: $url}) SET v.processedAt = $timestamp`,\n      params: { url: youtubeUrl, timestamp: new Date().toISOString() }\n    });\n\n    // Create Person node\n    queries.push({\n      query: `MERGE (p:Person {name: $name}) \n                SET p.role = $role, p.description = $description, p.topics = $topics, p.expertise = $expertise`,\n      params: {\n        name: persona.name,\n        role: persona.role || 'Unknown',\n        description: persona.description || '',\n        topics: persona.topics || [],\n        expertise: persona.expertise || []\n      }\n    });\n\n    // Create APPEARS_IN relationship\n    queries.push({\n      query: `MATCH (p:Person {name: $personName}), (v:Video {url: $videoUrl})\n                MERGE (p)-[:APPEARS_IN]->(v)`,\n      params: { personName: persona.name, videoUrl: youtubeUrl }\n    });\n\n    // Create entities and relationships\n    if (extractedKnowledge.entities) {\n      extractedKnowledge.entities.forEach(entity => {\n        const entityType = entity.type || 'Entity';\n        queries.push({\n          query: `MERGE (e:${entityType} {name: $name}) SET e.description = $description`,\n          params: { name: entity.name, description: entity.description || '' }\n        });\n\n        // Create relationship between person and entity\n        queries.push({\n          query: `MATCH (p:Person {name: $personName}), (e:${entityType} {name: $entityName})\n                    MERGE (p)-[:MENTIONS]->(e)`,\n          params: { personName: persona.name, entityName: entity.name }\n        });\n      });\n    }\n\n    // Create knowledge nodes\n    if (extractedKnowledge.knowledge) {\n      extractedKnowledge.knowledge.forEach(knowledge => {\n        queries.push({\n          query: `MERGE (k:Knowledge {domain: $domain}) SET k.description = $description`,\n          params: { domain: knowledge.domain || knowledge, description: knowledge.description || '' }\n        });\n\n        queries.push({\n          query: `MATCH (p:Person {name: $personName}), (k:Knowledge {domain: $domain})\n                    MERGE (p)-[:HAS_EXPERTISE]->(k)`,\n          params: { personName: persona.name, domain: knowledge.domain || knowledge }\n        });\n      });\n    }\n\n    return queries;\n  }\n\n  async storeKnowledgeGraph(knowledgeData) {\n    try {\n      const queries = this.generateCypherQueries(knowledgeData);\n      const results = [];\n\n      for (const { query, params } of queries) {\n        const result = await this.executeCypher(query, params);\n        results.push({ query, params, result });\n      }\n\n      return {\n        success: true,\n        queriesExecuted: queries.length,\n        results: results\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message,\n        queriesExecuted: 0\n      };\n    }\n  }\n}\n\n// Process all knowledge extraction results\nconst inputData = $input.all();\nconst storage = new MemgraphStorage();\nconst results = [];\n\nfor (const item of inputData) {\n  if (item.json.status === 'extraction_complete') {\n    const storageResult = await storage.storeKnowledgeGraph(item.json);\n    results.push({\n      json: {\n        ...item.json,\n        storageResult: storageResult,\n        storageTimestamp: new Date().toISOString(),\n        status: storageResult.success ? 'stored_successfully' : 'storage_failed'\n      }\n    });\n  } else {\n    results.push({\n      json: {\n        ...item.json,\n        storageResult: { success: false, error: 'Knowledge extraction failed' },\n        status: 'storage_skipped'\n      }\n    });\n  }\n}\n\nreturn results;"}, "id": "memgraph-storage-agent", "name": "Memgraph Storage Agent", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"jsCode": "// Comprehensive Test Suite - Validate entire workflow\nconst axios = require('axios');\n\nclass WorkflowTester {\n  constructor() {\n    this.testResults = {\n      totalTests: 0,\n      passed: 0,\n      failed: 0,\n      details: []\n    };\n  }\n\n  addTest(testName, passed, details) {\n    this.testResults.totalTests++;\n    if (passed) {\n      this.testResults.passed++;\n    } else {\n      this.testResults.failed++;\n    }\n    this.testResults.details.push({\n      test: testName,\n      status: passed ? 'PASSED' : 'FAILED',\n      details: details,\n      timestamp: new Date().toISOString()\n    });\n  }\n\n  async testEnvironmentVariables() {\n    const requiredVars = ['GOOGLE_API_KEY', 'MEMGRAPH_URI'];\n    let allPresent = true;\n    const missingVars = [];\n\n    requiredVars.forEach(varName => {\n      if (!process.env[varName]) {\n        allPresent = false;\n        missingVars.push(varName);\n      }\n    });\n\n    this.addTest(\n      'Environment Variables',\n      allPresent,\n      allPresent ? 'All required environment variables are set' : `Missing: ${missingVars.join(', ')}`\n    );\n\n    return allPresent;\n  }\n\n  async testGeminiAPIConnection() {\n    try {\n      const apiKey = process.env.GOOGLE_API_KEY;\n      if (!apiKey) {\n        this.addTest('Gemini API Connection', false, 'API key not available');\n        return false;\n      }\n\n      const response = await axios.post(\n        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`,\n        {\n          contents: [{\n            parts: [{\n              text: 'Hello, this is a test message. Please respond with \"API connection successful\".'\n            }]\n          }]\n        },\n        {\n          headers: { 'Content-Type': 'application/json' },\n          timeout: 10000\n        }\n      );\n\n      const success = response.status === 200;\n      this.addTest(\n        'Gemini API Connection',\n        success,\n        success ? 'API connection successful' : `HTTP ${response.status}`\n      );\n      return success;\n    } catch (error) {\n      this.addTest('Gemini API Connection', false, `Error: ${error.message}`);\n      return false;\n    }\n  }\n\n  async testMemgraphConnection() {\n    try {\n      const uri = process.env.MEMGRAPH_URI || 'bolt://localhost:7687';\n      const httpUri = uri.replace('bolt://', 'http://').replace(':7687', ':7444');\n      \n      const response = await axios.post(\n        `${httpUri}/db/data/transaction/commit`,\n        {\n          statements: [{\n            statement: 'RETURN 1 as test',\n            parameters: {}\n          }]\n        },\n        {\n          headers: { 'Content-Type': 'application/json' },\n          timeout: 5000\n        }\n      );\n\n      const success = response.status === 200;\n      this.addTest(\n        'Memgraph Database Connection',\n        success,\n        success ? 'Database connection successful' : `HTTP ${response.status}`\n      );\n      return success;\n    } catch (error) {\n      this.addTest('Memgraph Database Connection', false, `Error: ${error.message}`);\n      return false;\n    }\n  }\n\n  testDataIntegrity(inputData) {\n    let allValid = true;\n    const issues = [];\n\n    inputData.forEach((item, index) => {\n      const data = item.json;\n      \n      // Test video analysis results\n      if (!data.youtubeUrl) {\n        allValid = false;\n        issues.push(`Item ${index}: Missing YouTube URL`);\n      }\n      \n      if (!data.persona || !data.persona.name) {\n        allValid = false;\n        issues.push(`Item ${index}: Missing or invalid persona data`);\n      }\n      \n      if (!data.extractedKnowledge) {\n        allValid = false;\n        issues.push(`Item ${index}: Missing extracted knowledge`);\n      }\n      \n      if (!data.storageResult) {\n        allValid = false;\n        issues.push(`Item ${index}: Missing storage result`);\n      }\n    });\n\n    this.addTest(\n      'Data Integrity',\n      allValid,\n      allValid ? 'All data items are valid' : issues.join('; ')\n    );\n\n    return allValid;\n  }\n\n  testKnowledgeGraphStructure(inputData) {\n    let validStructure = true;\n    const structureIssues = [];\n\n    inputData.forEach((item, index) => {\n      const knowledge = item.json.extractedKnowledge;\n      \n      if (knowledge) {\n        if (!Array.isArray(knowledge.entities)) {\n          validStructure = false;\n          structureIssues.push(`Item ${index}: entities should be an array`);\n        }\n        \n        if (!Array.isArray(knowledge.relationships)) {\n          validStructure = false;\n          structureIssues.push(`Item ${index}: relationships should be an array`);\n        }\n        \n        if (!Array.isArray(knowledge.knowledge)) {\n          validStructure = false;\n          structureIssues.push(`Item ${index}: knowledge should be an array`);\n        }\n      }\n    });\n\n    this.addTest(\n      'Knowledge Graph Structure',\n      validStructure,\n      validStructure ? 'Knowledge graph structure is valid' : structureIssues.join('; ')\n    );\n\n    return validStructure;\n  }\n\n  async runAllTests(inputData) {\n    console.log('Starting comprehensive workflow tests...');\n    \n    await this.testEnvironmentVariables();\n    await this.testGeminiAPIConnection();\n    await this.testMemgraphConnection();\n    this.testDataIntegrity(inputData);\n    this.testKnowledgeGraphStructure(inputData);\n    \n    return this.testResults;\n  }\n}\n\n// Run comprehensive tests\nconst inputData = $input.all();\nconst tester = new WorkflowTester();\n\nconst testResults = await tester.runAllTests(inputData);\n\n// Generate summary report\nconst summary = {\n  workflowStatus: testResults.failed === 0 ? 'SUCCESS' : 'PARTIAL_SUCCESS',\n  testSummary: testResults,\n  processedItems: inputData.length,\n  successfullyStored: inputData.filter(item => item.json.status === 'stored_successfully').length,\n  timestamp: new Date().toISOString(),\n  recommendations: []\n};\n\n// Add recommendations based on test results\nif (testResults.failed > 0) {\n  summary.recommendations.push('Review failed tests and fix configuration issues');\n}\nif (summary.successfullyStored < inputData.length) {\n  summary.recommendations.push('Check storage failures and database connectivity');\n}\nif (summary.successfullyStored === 0) {\n  summary.recommendations.push('Verify API keys and database configuration');\n}\n\nreturn [{\n  json: {\n    ...summary,\n    inputData: inputData,\n    detailedResults: testResults.details\n  }\n}];"}, "id": "test-suite", "name": "Comprehensive Test Suite", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300]}], "connections": {"Start Workflow": {"main": [[{"node": "Input Processor Agent", "type": "main", "index": 0}]]}, "Input Processor Agent": {"main": [[{"node": "Video Analysis Agent", "type": "main", "index": 0}]]}, "Video Analysis Agent": {"main": [[{"node": "Knowledge Extraction Agent", "type": "main", "index": 0}]]}, "Knowledge Extraction Agent": {"main": [[{"node": "Memgraph Storage Agent", "type": "main", "index": 0}]]}, "Memgraph Storage Agent": {"main": [[{"node": "Comprehensive Test Suite", "type": "main", "index": 0}]]}}, "pinData": {}}