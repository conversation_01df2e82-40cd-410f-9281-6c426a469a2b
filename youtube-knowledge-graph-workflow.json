{"meta": {"templateCredsSetupCompleted": false, "instanceId": "youtube-knowledge-graph-system-v2"}, "name": "YouTube Knowledge Graph Multi-Agent System v2.0", "active": false, "nodes": [{"parameters": {"options": {}}, "id": "chat-trigger", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.chatTrigger", "typeVersion": 1, "position": [240, 200]}, {"parameters": {}, "id": "workflow-trigger", "name": "Workflow Trigger", "type": "n8n-nodes-base.workflowTrigger", "typeVersion": 1, "position": [240, 400]}, {"parameters": {"values": {"string": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "value": "YOUR_GOOGLE_API_KEY_HERE"}]}, "options": {}}, "id": "set-api-key", "name": "Set API Key", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [460, 300]}, {"parameters": {"jsCode": "// Input Processor Agent\\nconst inputData = $input.all();\\nlet inputText = '';\\n\\nif (inputData.length > 0) {\\n  const firstItem = inputData[0].json;\\n  if (firstItem.chatInput) {\\n    inputText = firstItem.chatInput;\\n  } else if (firstItem.youtubeUrls) {\\n    inputText = Array.isArray(firstItem.youtubeUrls) ? firstItem.youtubeUrls.join(' ') : firstItem.youtubeUrls;\\n  } else if (firstItem.input) {\\n    inputText = firstItem.input;\\n  } else {\\n    inputText = JSON.stringify(firstItem);\\n  }\\n}\\n\\nconst urlRegex = /(?:https?:\\/\\/)?(?:www\\.)?(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([a-zA-Z0-9_-]{11})/g;\\nconst foundUrls = [];\\nlet match;\\n\\nwhile ((match = urlRegex.exec(inputText)) !== null) {\\n  const fullUrl = match[0].startsWith('http') ? match[0] : `https://www.youtube.com/watch?v=${match[1]}`;\\n  foundUrls.push(fullUrl);\\n}\\n\\nconst youtubeUrls = foundUrls.length > 0 ? foundUrls : [\\n  'https://www.youtube.com/watch?v=dQw4w9WgXcQ'\\n];\\n\\nfunction isValidYouTubeUrl(url) {\\n  const regex = /^(https?:\\/\\/)?(www\\.)?(youtube\\.com|youtu\\.be)\\/.+/;\\n  return regex.test(url);\\n}\\n\\nconst validUrls = youtubeUrls.filter(url => isValidYouTubeUrl(url));\\n\\nreturn validUrls.map(url => ({\\n  json: {\\n    youtubeUrl: url,\\n    timestamp: new Date().toISOString(),\\n    status: 'ready_for_analysis',\\n    apiKey: $node['Set API Key'].json['apiKey']\\n  }\\n}));"}, "id": "input-processor", "name": "Input Processor Agent", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"batchSize": 1, "options": {}}, "id": "split-videos", "name": "Split Videos", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [900, 300]}, {"parameters": {"jsCode": "// Video Analysis Agent with Enhanced Persona Identification\\nconst axios = require('axios');\\n\\nasync function analyzeVideoWithGemini(youtubeUrl, apiKey) {\\n  if (!apiKey || apiKey === 'YOUR_GOOGLE_API_KEY_HERE') {\\n    throw new Error('Valid Google API key is required.');\\n  }\\n\\n  const prompt = 'You are an expert persona identifier. Your task is to analyze the provided YouTube video transcript and its metadata.\\n\\n**Your Goal:**\\n\\nIdentify all distinct personas present or significantly discussed in the transcript. A persona can be:\\n1. A **Named Individual**: A specific person mentioned by their canonical full name (e.g., ELON_MUSK, JOE_ROGAN).\\n2. A **Predefined Specialty Persona**: If the transcript contains substantial, extractable information that can be attributed to or defines one of the following broad specialty roles *itself*, then list that specialty. The allowed specialty persona names are strictly:\\n   * PSYCHOLOGIST\\n   * LAWYER\\n   * DOCTOR\\n   * SOFTWARE_ENGINEER\\n   * CEO\\n\\n**Important Considerations for Specialty Personas:**\\n* Only include a specialty persona (e.g., CEO) if the transcript discusses the *role, principles, or general characteristics* of that specialty, not just because a named individual *happens to have that profession*.\\n\\n**For each identified persona, provide:**\\n1. Full name (use canonical format like FIRST_LAST for named individuals, or specialty name for roles)\\n2. Role or title\\n3. Brief description\\n4. Key topics they discuss or are associated with\\n5. Their expertise areas\\n\\n**Structure your response as a JSON array of persona objects with the following format:**\\n[{\\n  \"name\": \"PERSON_NAME or SPECIALTY_ROLE\",\\n  \"role\": \"Their Role/Title\",\\n  \"description\": \"Brief description of the persona\",\\n  \"topics\": [\"topic1\", \"topic2\", \"topic3\"],\\n  \"expertise\": [\"skill1\", \"skill2\", \"skill3\"],\\n  \"personaType\": \"NAMED_INDIVIDUAL\" or \"SPECIALTY_PERSONA\"\\n}]\\n\\n**Examples:**\\n- If discussing Elon Musk specifically: {\"name\": \"ELON_MUSK\", \"personaType\": \"NAMED_INDIVIDUAL\"}\\n- If discussing CEO leadership principles in general: {\"name\": \"CEO\", \"personaType\": \"SPECIALTY_PERSONA\"}\\n- If Elon Musk talks about being a CEO but focuses on him personally: {\"name\": \"ELON_MUSK\", \"personaType\": \"NAMED_INDIVIDUAL\"} (not CEO specialty)\\n\\nAnalyze the video content carefully and extract all relevant personas according to these guidelines.';\\n\\n  try {\\n    const response = await axios.post(\\n      `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`,\\n      {\\n        contents: [{\\n          parts: [\\n            { file_data: { file_uri: youtubeUrl } },\\n            { text: prompt }\\n          ]\\n        }],\\n        generationConfig: {\\n          temperature: 0.1,\\n          topK: 40,\\n          topP: 0.95,\\n          maxOutputTokens: 8192\\n        }\\n      },\\n      { headers: { 'Content-Type': 'application/json' } }\\n    );\\n\\n    const content = response.data.candidates[0].content.parts[0].text;\\n    let personas;\\n    \\n    try {\\n      const jsonMatch = content.match(/\\\\[.*\\\\]/s);\\n      personas = jsonMatch ? JSON.parse(jsonMatch[0]) : [];\\n    } catch (parseError) {\\n      personas = [{\\n        name: 'Unknown Speaker',\\n        role: 'Video Participant',\\n        description: content.substring(0, 200),\\n        topics: ['General Discussion'],\\n        expertise: ['Communication'],\\n        personaType: 'NAMED_INDIVIDUAL'\\n      }];\\n    }\\n\\n    return {\\n      youtubeUrl,\\n      personas,\\n      analysisTimestamp: new Date().toISOString(),\\n      status: 'analysis_complete',\\n      apiKey\\n    };\\n  } catch (error) {\\n    return {\\n      youtubeUrl,\\n      personas: [],\\n      error: error.message,\\n      status: 'analysis_failed',\\n      apiKey\\n    };\\n  }\\n}\\n\\nconst inputData = $input.all();\\nconst currentItem = inputData[0].json;\\nconst result = await analyzeVideoWithGemini(currentItem.youtubeUrl, currentItem.apiKey);\\nreturn [{ json: result }];"}, "id": "video-analysis-agent", "name": "Video Analysis Agent", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"batchSize": 1, "options": {}}, "id": "split-personas", "name": "Split Personas", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1340, 300]}, {"parameters": {"jsCode": "// Prepare Personas for Processing\\nconst inputData = $input.all();\\nconst videoData = inputData[0].json;\\n\\nif (!videoData.personas || videoData.personas.length === 0) {\\n  return [{\\n    json: {\\n      youtubeUrl: videoData.youtubeUrl,\\n      persona: null,\\n      apiKey: videoData.apiKey,\\n      status: 'no_personas_found',\\n      timestamp: new Date().toISOString()\\n    }\\n  }];\\n}\\n\\nconst personaItems = videoData.personas.map(persona => ({\\n  json: {\\n    youtubeUrl: videoData.youtubeUrl,\\n    persona: persona,\\n    apiKey: videoData.apiKey,\\n    analysisTimestamp: videoData.analysisTimestamp,\\n    status: 'ready_for_knowledge_extraction',\\n    timestamp: new Date().toISOString()\\n  }\\n}));\\n\\nreturn personaItems;"}, "id": "prepare-personas", "name": "Prepare <PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 300]}, {"parameters": {"jsCode": "// Knowledge Extraction Agent\\nconst axios = require('axios');\\n\\nasync function extractKnowledgeForPersona(persona, youtubeUrl, apiKey) {\\n  if (!apiKey || apiKey === 'YOUR_GOOGLE_API_KEY_HERE') {\\n    throw new Error('Valid Google API key is required.');\\n  }\\n\\n  const prompt = `For the persona \\\"${persona.name}\\\" (${persona.role}) from the video ${youtubeUrl}, extract comprehensive knowledge including:\\n\\n1. ENTITIES:\\n   - People they mention or work with\\n   - Organizations they're affiliated with\\n   - Concepts, technologies, or methodologies they discuss\\n   - Projects or products they're involved in\\n\\n2. RELATIONSHIPS:\\n   - Professional relationships\\n   - Collaborations\\n   - Influences or mentorships\\n   - Organizational connections\\n\\n3. KNOWLEDGE AREAS:\\n   - Expertise domains\\n   - Skills and competencies\\n   - Research interests\\n   - Industry knowledge\\n\\nStructure your response as JSON with the following format:\\n{\\n  \\\"entities\\\": [\\n    {\\\"name\\\": \\\"Entity Name\\\", \\\"type\\\": \\\"Person|Organization|Concept\\\", \\\"description\\\": \\\"Brief description\\\"}\\n  ],\\n  \\\"relationships\\\": [\\n    {\\\"from\\\": \\\"Source\\\", \\\"to\\\": \\\"Target\\\", \\\"type\\\": \\\"RELATIONSHIP_TYPE\\\", \\\"description\\\": \\\"Description\\\"}\\n  ],\\n  \\\"knowledge\\\": [\\n    {\\\"domain\\\": \\\"Knowledge Domain\\\", \\\"description\\\": \\\"Detailed description\\\", \\\"level\\\": \\\"Beginner|Intermediate|Expert\\\"}\\n  ]\\n}`;\\n\\n  try {\\n    const response = await axios.post(\\n      `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`,\\n      {\\n        contents: [{ parts: [{ text: prompt }] }],\\n        generationConfig: { temperature: 0.1, topK: 40, topP: 0.95, maxOutputTokens: 8192 }\\n      },\\n      { headers: { 'Content-Type': 'application/json' } }\\n    );\\n\\n    const content = response.data.candidates[0].content.parts[0].text;\\n    let knowledgeData;\\n    \\n    try {\\n      const jsonMatch = content.match(/\\\\{.*\\\\}/s);\\n      knowledgeData = jsonMatch ? JSON.parse(jsonMatch[0]) : { entities: [], relationships: [], knowledge: [] };\\n    } catch (parseError) {\\n      knowledgeData = {\\n        entities: [{ name: persona.name, type: 'Person', description: persona.description }],\\n        relationships: [],\\n        knowledge: (persona.expertise || []).map(exp => ({ domain: exp, description: '', level: 'Unknown' }))\\n      };\\n    }\\n\\n    return {\\n      persona,\\n      youtubeUrl,\\n      extractedKnowledge: knowledgeData,\\n      extractionTimestamp: new Date().toISOString(),\\n      status: 'extraction_complete',\\n      apiKey\\n    };\\n  } catch (error) {\\n    return {\\n      persona,\\n      youtubeUrl,\\n      extractedKnowledge: { entities: [], relationships: [], knowledge: [] },\\n      error: error.message,\\n      status: 'extraction_failed',\\n      apiKey\\n    };\\n  }\\n}\\n\\nconst inputData = $input.all();\\nconst currentItem = inputData[0].json;\\n\\nif (!currentItem.persona) {\\n  return [{ json: { ...currentItem, status: 'skipped_no_persona' } }];\\n}\\n\\nconst result = await extractKnowledgeForPersona(currentItem.persona, currentItem.youtubeUrl, currentItem.apiKey);\\nreturn [{ json: result }];"}, "id": "knowledge-extraction-agent", "name": "Knowledge Extraction Agent", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 300]}, {"parameters": {"jsCode": "// Memgraph Storage Agent - Store in Ai-personas collection\\nconst axios = require('axios');\\n\\nclass MemgraphStorage {\\n  constructor() {\\n    this.httpUri = 'http://localhost:7444';\\n    this.collection = 'Ai-personas';\\n  }\\n\\n  async executeCypher(query, parameters = {}) {\\n    const endpoints = [`${this.httpUri}/db/data/transaction/commit`, `${this.httpUri}/query`];\\n    \\n    for (const endpoint of endpoints) {\\n      try {\\n        const response = await axios.post(endpoint, {\\n          statements: [{ statement: query, parameters }]\\n        }, {\\n          headers: { 'Content-Type': 'application/json' },\\n          timeout: 10000\\n        });\\n        if (response.status === 200) return response.data;\\n      } catch (error) {\\n        continue;\\n      }\\n    }\\n    throw new Error('All Memgraph endpoints failed');\\n  }\\n\\n  generateCypherQueries(knowledgeData) {\\n    const queries = [];\\n    const { persona, youtubeUrl, extractedKnowledge } = knowledgeData;\\n    const timestamp = new Date().toISOString();\\n\\n    // Create Video node\\n    queries.push({\\n      query: 'MERGE (v:Video:AiPersonas {url: $url}) SET v.processedAt = $timestamp, v.collection = $collection',\\n      params: { url: youtubeUrl, timestamp, collection: this.collection }\\n    });\\n\\n    // Create Person node\\n    queries.push({\\n      query: 'MERGE (p:Person:AiPersonas {name: $name}) SET p.role = $role, p.description = $description, p.topics = $topics, p.expertise = $expertise, p.collection = $collection, p.updatedAt = $timestamp, p.personaType = $personaType',\\n      params: {\\n        name: persona.name,\\n        role: persona.role || 'Unknown',\\n        description: persona.description || '',\\n        topics: persona.topics || [],\\n        expertise: persona.expertise || [],\\n        personaType: persona.personaType || 'NAMED_INDIVIDUAL',\\n        collection: this.collection,\\n        timestamp\\n      }\\n    });\\n\\n    // Create APPEARS_IN relationship\\n    queries.push({\\n      query: 'MATCH (p:Person:AiPersonas {name: $personName}), (v:Video:AiPersonas {url: $videoUrl}) MERGE (p)-[:APPEARS_IN {collection: $collection, createdAt: $timestamp}]->(v)',\\n      params: { personName: persona.name, videoUrl: youtubeUrl, collection: this.collection, timestamp }\\n    });\\n\\n    // Create entities and relationships\\n    if (extractedKnowledge.entities) {\\n      extractedKnowledge.entities.forEach(entity => {\\n        const entityType = entity.type || 'Entity';\\n        queries.push({\\n          query: `MERGE (e:${entityType}:AiPersonas {name: $name}) SET e.description = $description, e.collection = $collection, e.updatedAt = $timestamp`,\\n          params: { name: entity.name, description: entity.description || '', collection: this.collection, timestamp }\\n        });\\n        queries.push({\\n          query: `MATCH (p:Person:AiPersonas {name: $personName}), (e:${entityType}:AiPersonas {name: $entityName}) MERGE (p)-[:MENTIONS {collection: $collection, createdAt: $timestamp}]->(e)`,\\n          params: { personName: persona.name, entityName: entity.name, collection: this.collection, timestamp }\\n        });\\n      });\\n    }\\n\\n    // Create knowledge nodes\\n    if (extractedKnowledge.knowledge) {\\n      extractedKnowledge.knowledge.forEach(knowledge => {\\n        const domain = knowledge.domain || knowledge;\\n        queries.push({\\n          query: 'MERGE (k:Knowledge:AiPersonas {domain: $domain}) SET k.description = $description, k.level = $level, k.collection = $collection, k.updatedAt = $timestamp',\\n          params: { domain, description: knowledge.description || '', level: knowledge.level || 'Unknown', collection: this.collection, timestamp }\\n        });\\n        queries.push({\\n          query: 'MATCH (p:Person:AiPersonas {name: $personName}), (k:Knowledge:AiPersonas {domain: $domain}) MERGE (p)-[:HAS_EXPERTISE {collection: $collection, level: $level, createdAt: $timestamp}]->(k)',\\n          params: { personName: persona.name, domain, level: knowledge.level || 'Unknown', collection: this.collection, timestamp }\\n        });\\n      });\\n    }\\n\\n    return queries;\\n  }\\n\\n  async storeKnowledgeGraph(knowledgeData) {\\n    try {\\n      const queries = this.generateCypherQueries(knowledgeData);\\n      const results = [];\\n      let successCount = 0;\\n\\n      for (const { query, params } of queries) {\\n        try {\\n          const result = await this.executeCypher(query, params);\\n          results.push({ query, params, result, success: true });\\n          successCount++;\\n        } catch (error) {\\n          results.push({ query, params, error: error.message, success: false });\\n        }\\n      }\\n\\n      return {\\n        success: successCount > 0,\\n        queriesExecuted: queries.length,\\n        successfulQueries: successCount,\\n        results,\\n        collection: this.collection\\n      };\\n    } catch (error) {\\n      return { success: false, error: error.message, queriesExecuted: 0, collection: this.collection };\\n    }\\n  }\\n}\\n\\nconst inputData = $input.all();\\nconst currentItem = inputData[0].json;\\nconst storage = new MemgraphStorage();\\n\\nif (currentItem.status === 'extraction_complete') {\\n  const storageResult = await storage.storeKnowledgeGraph(currentItem);\\n  return [{ json: { ...currentItem, storageResult, storageTimestamp: new Date().toISOString(), status: storageResult.success ? 'stored_successfully' : 'storage_failed' } }];\\n} else {\\n  return [{ json: { ...currentItem, storageResult: { success: false, error: 'Knowledge extraction failed' }, status: 'storage_skipped' } }];\\n}"}, "id": "memgraph-storage-agent", "name": "Memgraph Storage Agent", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2000, 300]}, {"parameters": {"jsCode": "// Comprehensive Test Suite\\nconst axios = require('axios');\\n\\nclass WorkflowTester {\\n  constructor() {\\n    this.testResults = { totalTests: 0, passed: 0, failed: 0, details: [] };\\n  }\\n\\n  addTest(testName, passed, details) {\\n    this.testResults.totalTests++;\\n    if (passed) this.testResults.passed++;\\n    else this.testResults.failed++;\\n    this.testResults.details.push({ test: testName, status: passed ? 'PASSED' : 'FAILED', details, timestamp: new Date().toISOString() });\\n  }\\n\\n  async testGeminiAPIConnection() {\\n    try {\\n      const apiKey = $node['Set API Key'].json['apiKey'];\\n      if (!apiKey || apiKey === 'YOUR_GOOGLE_API_KEY_HERE') {\\n        this.addTest('Gemini API Connection', false, 'API key not configured');\\n        return false;\\n      }\\n      const response = await axios.post(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`, {\\n        contents: [{ parts: [{ text: 'Test message' }] }]\\n      }, { headers: { 'Content-Type': 'application/json' }, timeout: 10000 });\\n      const success = response.status === 200;\\n      this.addTest('Gemini API Connection', success, success ? 'API connection successful' : `HTTP ${response.status}`);\\n      return success;\\n    } catch (error) {\\n      this.addTest('Gemini API Connection', false, `Error: ${error.message}`);\\n      return false;\\n    }\\n  }\\n\\n  async testMemgraphConnection() {\\n    try {\\n      const response = await axios.post('http://localhost:7444/db/data/transaction/commit', {\\n        statements: [{ statement: 'RETURN 1 as test', parameters: {} }]\\n      }, { headers: { 'Content-Type': 'application/json' }, timeout: 5000 });\\n      const success = response.status === 200;\\n      this.addTest('Memgraph Database Connection', success, success ? 'Database connection successful' : `HTTP ${response.status}`);\\n      return success;\\n    } catch (error) {\\n      this.addTest('Memgraph Database Connection', false, `Error: ${error.message}`);\\n      return false;\\n    }\\n  }\\n\\n  testDataIntegrity(inputData) {\\n    let allValid = true;\\n    const issues = [];\\n    inputData.forEach((item, index) => {\\n      const data = item.json;\\n      if (!data.youtubeUrl) { allValid = false; issues.push(`Item ${index}: Missing YouTube URL`); }\\n      if (!data.persona || !data.persona.name) { allValid = false; issues.push(`Item ${index}: Missing persona data`); }\\n      if (!data.extractedKnowledge) { allValid = false; issues.push(`Item ${index}: Missing extracted knowledge`); }\\n      if (!data.storageResult) { allValid = false; issues.push(`Item ${index}: Missing storage result`); }\\n    });\\n    this.addTest('Data Integrity', allValid, allValid ? 'All data items are valid' : issues.join('; '));\\n    return allValid;\\n  }\\n\\n  async runAllTests(inputData) {\\n    await this.testGeminiAPIConnection();\\n    await this.testMemgraphConnection();\\n    this.testDataIntegrity(inputData);\\n    return this.testResults;\\n  }\\n}\\n\\nconst inputData = $input.all();\\nconst tester = new WorkflowTester();\\nconst testResults = await tester.runAllTests(inputData);\\n\\nconst summary = {\\n  workflowStatus: testResults.failed === 0 ? 'SUCCESS' : 'PARTIAL_SUCCESS',\\n  testSummary: testResults,\\n  processedItems: inputData.length,\\n  successfullyStored: inputData.filter(item => item.json.status === 'stored_successfully').length,\\n  timestamp: new Date().toISOString(),\\n  recommendations: testResults.failed > 0 ? ['Review failed tests and fix configuration issues'] : ['Workflow completed successfully']\\n};\\n\\nreturn [{ json: { ...summary, inputData, detailedResults: testResults.details } }];"}, "id": "test-suite", "name": "Comprehensive Test Suite", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2220, 300]}], "connections": {"Chat Trigger": {"main": [[{"node": "Set API Key", "type": "main", "index": 0}]]}, "Workflow Trigger": {"main": [[{"node": "Set API Key", "type": "main", "index": 0}]]}, "Set API Key": {"main": [[{"node": "Input Processor Agent", "type": "main", "index": 0}]]}, "Input Processor Agent": {"main": [[{"node": "Split Videos", "type": "main", "index": 0}]]}, "Split Videos": {"main": [[{"node": "Video Analysis Agent", "type": "main", "index": 0}]]}, "Video Analysis Agent": {"main": [[{"node": "Split Personas", "type": "main", "index": 0}]]}, "Split Personas": {"main": [[{"node": "Prepare <PERSON>", "type": "main", "index": 0}]]}, "Prepare Personas": {"main": [[{"node": "Knowledge Extraction Agent", "type": "main", "index": 0}]]}, "Knowledge Extraction Agent": {"main": [[{"node": "Memgraph Storage Agent", "type": "main", "index": 0}]]}, "Memgraph Storage Agent": {"main": [[{"node": "Comprehensive Test Suite", "type": "main", "index": 0}]]}}, "pinData": {}}