{"name": "youtube-knowledge-graph-workflow", "version": "1.0.0", "description": "A multi-agent n8n workflow system that processes YouTube videos to create comprehensive knowledge graphs using Google Gemini 2.5 Flash and Memgraph database", "main": "test-workflow.js", "scripts": {"test": "node test-workflow.js", "start": "node test-workflow.js", "setup": "npm install && echo 'Setup complete! Please configure your environment variables.'", "validate": "node test-workflow.js"}, "keywords": ["n8n", "workflow", "youtube", "knowledge-graph", "gemini", "memgraph", "ai", "multi-agent", "langchain"], "author": "YouTube Knowledge Graph System", "license": "MIT", "dependencies": {"axios": "^1.6.0", "dotenv": "^16.3.0"}, "devDependencies": {}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/youtube-knowledge-graph-workflow.git"}, "bugs": {"url": "https://github.com/your-username/youtube-knowledge-graph-workflow/issues"}, "homepage": "https://github.com/your-username/youtube-knowledge-graph-workflow#readme", "files": ["youtube-knowledge-graph-workflow.json", "test-workflow.js", "README.md", "package.json"]}