# YouTube Knowledge Graph Multi-Agent System

A sophisticated n8n workflow that processes YouTube videos to create comprehensive knowledge graphs using Google Gemini 2.5 Flash and Memgraph database.

## 🎯 Overview

This multi-agent system analyzes YouTube videos to identify personas and extract their associated knowledge, relationships, and expertise, then stores everything in a Memgraph knowledge graph database.

## 🏗️ Architecture

### Multi-Agent Workflow Components

1. **Input Processor Agent** - Validates and processes YouTube URLs
2. **Video Analysis Agent** - Uses Gemini 2.5 Flash to identify people/personas in videos
3. **Knowledge Extraction Agent** - Extracts detailed knowledge, entities, and relationships for each persona
4. **Memgraph Storage Agent** - Stores knowledge graph data in Memgraph database
5. **Comprehensive Test Suite** - Validates entire workflow functionality

### Technology Stack

- **n8n**: Workflow orchestration platform
- **Google Gemini 2.5 Flash**: AI model for YouTube video analysis
- **Memgraph**: Graph database for knowledge storage
- **LangChain**: Framework patterns for knowledge extraction
- **Node.js**: Runtime environment for workflow execution

## 🚀 Features

- **YouTube URL Processing**: Direct analysis of YouTube videos using Gemini 2.5 Flash
- **Persona Identification**: Automatic detection of people appearing or mentioned in videos
- **Knowledge Extraction**: Comprehensive extraction of entities, relationships, and expertise
- **Graph Database Storage**: Structured storage in Memgraph with proper relationships
- **Comprehensive Testing**: Built-in test suite for validation and debugging
- **Environment Variable Configuration**: Secure API key and database credential management
- **Error Handling**: Robust error handling and logging throughout the workflow

## 📋 Prerequisites

### Required Services

1. **Google AI Studio Account**
   - Get your API key from [Google AI Studio](https://aistudio.google.com/)
   - Ensure access to Gemini 2.5 Flash model

2. **Memgraph Database**
   - Install locally or use cloud instance
   - Default connection: `bolt://localhost:7687`

3. **n8n Platform**
   - Self-hosted or cloud instance
   - Node.js environment with axios support

### Environment Variables

Set the following environment variables:

```bash
# Required
GOOGLE_API_KEY=your_google_ai_studio_api_key

# Optional (defaults provided)
MEMGRAPH_URI=bolt://localhost:7687
MEMGRAPH_USERNAME=
MEMGRAPH_PASSWORD=
```

## 🛠️ Installation & Setup

### 1. Memgraph Database Setup

```bash
# Using Docker
docker run -p 7687:7687 -p 7444:7444 memgraph/memgraph-mage --schema-info-enabled=True

# Or install locally following Memgraph documentation
```

### 2. Environment Configuration

Create a `.env` file or set environment variables:

```bash
export GOOGLE_API_KEY="your_api_key_here"
export MEMGRAPH_URI="bolt://localhost:7687"
export MEMGRAPH_USERNAME=""
export MEMGRAPH_PASSWORD=""
```

### 3. n8n Workflow Import

1. Open your n8n instance
2. Go to Workflows → Import from File
3. Select `youtube-knowledge-graph-workflow.json`
4. Activate the workflow

## 🎮 Usage

### Basic Workflow Execution

1. **Start the Workflow**: Click "Execute workflow" on the manual trigger
2. **Monitor Progress**: Watch each agent process the data sequentially
3. **Review Results**: Check the test suite output for validation results

### Customizing YouTube URLs

Edit the Input Processor Agent code to modify the YouTube URLs:

```javascript
const youtubeUrls = [
  'https://www.youtube.com/watch?v=your_video_id_1',
  'https://www.youtube.com/watch?v=your_video_id_2',
  // Add more URLs as needed
];
```

### Knowledge Graph Structure

The system creates the following node types and relationships:

**Node Types:**
- `Person`: Individuals identified in videos
- `Video`: YouTube videos processed
- `Organization`: Companies, institutions mentioned
- `Knowledge`: Expertise domains and skills
- `Entity`: General entities (concepts, technologies, etc.)

**Relationship Types:**
- `APPEARS_IN`: Person appears in Video
- `MENTIONS`: Person mentions Entity
- `HAS_EXPERTISE`: Person has expertise in Knowledge domain
- `WORKS_FOR`: Person works for Organization

## 🧪 Testing

The workflow includes a comprehensive test suite that validates:

- Environment variable configuration
- Google Gemini API connectivity
- Memgraph database connectivity
- Data integrity throughout the pipeline
- Knowledge graph structure validation

### Test Results Interpretation

- **SUCCESS**: All tests passed, workflow functioning correctly
- **PARTIAL_SUCCESS**: Some tests failed, check recommendations
- **FAILURE**: Critical issues detected, review configuration

## 🔧 Troubleshooting

### Common Issues

1. **API Key Issues**
   - Verify `GOOGLE_API_KEY` is set correctly
   - Check API key permissions in Google AI Studio

2. **Database Connection**
   - Ensure Memgraph is running on specified port
   - Verify network connectivity to database

3. **YouTube URL Processing**
   - Ensure URLs are public (not private/unlisted)
   - Check video length limitations (free tier: 8 hours/day)

### Debug Mode

Enable verbose logging by modifying the Code nodes to include:

```javascript
console.log('Debug info:', JSON.stringify(data, null, 2));
```

## 📊 Data Flow

```
YouTube URLs → Video Analysis → Persona Identification → Knowledge Extraction → Graph Storage → Validation
```

1. **Input**: List of YouTube URLs
2. **Analysis**: Gemini 2.5 Flash identifies personas
3. **Extraction**: Detailed knowledge extraction for each persona
4. **Storage**: Cypher queries store data in Memgraph
5. **Testing**: Comprehensive validation of entire pipeline

## 🔄 Updates & Changes

### Version 2.0.0 (Enhanced Multi-Agent System)
- **NEW**: Chat Trigger for conversational interaction
- **NEW**: Workflow Trigger for programmatic access
- **NEW**: Internal API key management (no environment variables needed)
- **NEW**: Proper looping with Split In Batches nodes
- **NEW**: Sequential processing of videos and personas
- **NEW**: Ai-personas collection integration
- **NEW**: Docker Memgraph configuration support
- **IMPROVED**: Enhanced error handling and logging
- **IMPROVED**: Better data flow structure
- **IMPROVED**: Comprehensive knowledge graph relationships

### Version 1.0.0 (Initial Release)
- Multi-agent workflow architecture
- Google Gemini 2.5 Flash integration
- Memgraph knowledge graph storage
- Comprehensive test suite
- Environment variable configuration
- Error handling and logging

### Future Enhancements
- Batch processing optimization
- Advanced relationship detection
- Custom entity type definitions
- Real-time streaming capabilities
- Enhanced visualization tools
- Integration with Memgraph Lab for visualization

## 📝 License

This project is provided as-is for educational and development purposes.

## 🤝 Contributing

Feel free to submit issues, feature requests, or improvements to enhance the workflow functionality.

## 📚 Additional Resources

- [n8n Documentation](https://docs.n8n.io/)
- [Google Gemini API Documentation](https://ai.google.dev/docs)
- [Memgraph Documentation](https://memgraph.com/docs)
- [LangChain Documentation](https://python.langchain.com/docs/)

## 🏆 Key Benefits

- **Scalable**: Process multiple YouTube videos efficiently
- **Intelligent**: Advanced AI-powered persona and knowledge extraction
- **Structured**: Well-organized knowledge graph storage
- **Testable**: Comprehensive validation and testing framework
- **Configurable**: Environment-based configuration management
- **Extensible**: Easy to modify and extend for specific use cases
